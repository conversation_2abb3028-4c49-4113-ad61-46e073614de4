<template>
	<LoadingModal v-if="data.type === 'loading'" id="loading_modal" :title="data.title" :message="data.message" />
	<ErrorModal v-if="data.type === 'error'" id="error_modal" :title="data.title" :message="data.message" :action="data.action" />
	<SuccessModal v-if="data.type === 'success'" id="success_modal" :title="data.title" :message="data.message" :action="data.action" />
</template>

<script setup lang="ts">
import ErrorModal from './ErrorModal.vue';
import LoadingModal from './LoadingModal.vue';
import SuccessModal from './SuccessModal.vue';

const props = defineProps<{
	data: {
		type: "loading",
		title: string,
		message: string
		action: {
			name: string,
			onClick: () => void,
		}
	} | {
		type: "error",
		title: string,
		message: string,
		action: {
			name: string,
			onClick: () => void,
		}
	} | {
		type: "success",
		title: string,
		message: string,
		action: {
			name: string,
			onClick: () => void,
		}
	}
}>()
</script>

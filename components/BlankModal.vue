<template>
	<dialog :id="id" class="z-0 modal">
		<div class="modal-box rounded-xl bg-modal border-textbox-border" :style="{ maxWidth: '408px' }">
			<div class="mt-0 modal-action">
				<form method="dialog">
					<button class="text-lg btn btn-xs btn-circle btn-ghost hover:bg-transparent focus:outline-none">✕</button>
				</form>
			</div>
			<slot />
		</div>
		<form method="dialog" class="modal-backdrop">
			<button>close</button>
		</form>
	</dialog>
</template>

<script setup>

const props = defineProps({
	id: String,
})

</script>

<style scoped></style>

<template>
  <div>
    <div v-if="isPending">
      <div class="mb-5 animate-pulse text-xl">
        Listing is being processed ...
      </div>
    </div>
    <div v-else class="hidden md:block">
      <div class="mb-8 flex justify-between">
        <div
          class="w-[50px] h-[26px] bg-secondary rounded-sm animate-pulse"
        ></div>
        <div
          class="w-[88px] h-[26px] bg-secondary rounded-sm animate-pulse"
        ></div>
      </div>
    </div>
    <div class="mb-5 flex flex-col md:flex-row gap-5">
      <div
        class="w-full md:w-1/2 h-[230px] md:h-[556px] bg-secondary rounded-xl animate-pulse"
      ></div>
      <div
        class="w-full md:w-1/2 h-[528px] md:h-[556px] bg-secondary rounded-xl animate-pulse"
      ></div>
    </div>
    <div class="mb-[56px] md:mb-20">
      <div
        class="w-full h-[522px] md:h-[533px] bg-secondary rounded-xl animate-pulse"
      ></div>
    </div>
    <div>
      <div class="flex justify-between items-center pb-4">
        <div class="flex gap-4 text-2sm">
          <div
            v-for="i in new Array(2)"
            class="w-[50px] h-[21px] bg-secondary animate-pulse"
          ></div>
        </div>

        <div class="w-8 h-8 bg-secondary animate-pulse md:hidden"></div>
        <div class="hidden md:block">
          <div class="flex gap-8">
            <div
              class="w-[118px] h-10 bg-secondary animate-pulse rounded-lg"
            ></div>
            <div
              class="w-[220px] h-10 bg-secondary animate-pulse rounded-lg"
            ></div>
          </div>
        </div>
      </div>
      <div
        class="w-[120px] h-4 bg-secondary animate-pulse md:hidden mb-5"
      ></div>
      <div class="py-4 hidden lg:block">
        <div class="flex items-center gap-2 mb-4" v-for="i in new Array(6)">
          <div
            class="w-[44px] min-w-[44px] h-[44px] max-h-[44px] bg-secondary rounded-full animate-pulse"
          ></div>
          <div
            class="w-[140px] h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>

          <div
            class="w-full max-w-full h-[20px] bg-secondary rounded-full animate-pulse"
          ></div>
        </div>
      </div>
      <div class="lg:hidden">
        <div class="flex flex-col mb-4" v-for="i in new Array(3)">
          <div
            class="min-h-[142px] w-full bg-secondary rounded-lg animate-pulse"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  isPending: {
    type: Boolean,
    default: false,
  },
});

watch(props.isPending, (newValue) => {
  console.log("props.isPending newValue", newValue);
});
</script>

<style lang="scss" scoped></style>

<template>
  <div
    v-if="lot"
    class="bg-white bg-opacity-[6%] pl-4 py-6 md:p-8 rounded-[12px] font-medium h-[522px]"
  >
    <div class="flex justify-between">
      <h5 class="text-white text-2sm md:text-base">Vesting Schedule</h5>
      <!-- <div class="flex gap-5">
        <div>start : {{ dayjs(lot.start_date * 1).format("DD-MM-YYYY") }}</div>
        <div>
          cliff end :
          {{
            dayjs(lot.start_date * 1)
              .add(lot.cliff_duration, timeUnit)
              .format("DD-MM-YYYY")
          }}
        </div>
        <div>end : {{ dayjs(lot.end_date * 1).format("DD-MM-YYYY") }}</div>
        <div>cycle : {{ lot.cycle }}</div>
        <div>duration : {{ lot.duration }}</div>

      </div> -->
      <!-- <div class="flex items-center gap-2">
          <inline-svg :src="claimIcon"></inline-svg>
          <div class="mr-4 text-input-icons">
              Claimable : <span class="text-[18px] text-white">12,000 {{ currentToken.token_ticker }}</span>
          </div>
          <button class="bg-primary min-h-[26px] max-h-[26px] w-[68px] text-2sm rounded-[4px] text-base-100 hover:opacity-60">Claim</button>
        </div> -->
    </div>
    <div class="pt-5 md:pt-6 w-full h-[408px]">
      <h3 class="mb-4 text-sm text-white/50">Token Release</h3>
      <canvas id="myChart"></canvas>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import claimIcon from "~/assets/images_new/icons/claim.svg";
import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";
import utc from "dayjs/plugin/utc";
import { TIME_UNIT } from "~/utils/const";
import { divideNumberUsingDecimals } from "~/utils/number";

const web3Store = useWeb3Store();
const props = defineProps({
  lot: Object,
  currentToken: Object,
});

dayjs.extend(advancedFormat);
dayjs.extend(utc);
const { $chart, $dayjs } = useNuxtApp();

const chart = ref(null);
const currentToken = ref(null);
const timeUnit = TIME_UNIT;

onMounted(async () => {
  lot.value = props.lot;
  currentToken.value = props.currentToken;

  await nextTick();

  setTimeout(() => {
    generateTimeChart();
  }, 700);
});

function generateDatesWithInterval(startDate, endDate, intervalDays) {
  const startDay = dayjs(startDate * 1); // Parse start date from Unix timestamp
  const endDay = dayjs(endDate * 1); // Parse end date from Unix timestamp
  const dateArray = [];

  // Loop to generate dates based on the specified interval
  for (
    let currentDate = startDay;
    currentDate.isBefore(endDay) || currentDate.isSame(endDay);
    currentDate = currentDate.add(intervalDays, timeUnit)
  ) {
    dateArray.push(currentDate); // Format and push to array
  }

  return dateArray;
}

const isChartReady = ref(false);
const lot = ref(null);
const diffMonth = computed(() => {
  let start = $dayjs(lot.value.start_date);
  let end = $dayjs(lot.value.end_date);

  // console.log(start.format('DD/MM/YYYY HH:mm'),end.format('DD/MM/YYYY HH:mm'))
  return end.diff(start, "month");
});

//for gradient
// const ctx = document.getElementById("myChart");
// const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
// gradient.addColorStop(0, "rgba(80, 241, 135, 0.2)");

function generateTimeChart() {
  console.log("lot", JSON.stringify(lot.value));
  isChartReady.value = false;

  if (chart.value) {
    chart.value.destroy();
  }

  const totalPoints = window.innerWidth > 820 ? 10 : 4;
  const startDate = dayjs(lot.value.start_date);
  const endDate = dayjs(lot.value.end_date);
  const cliffEndDate = startDate.add(lot.value.cliff_duration, timeUnit);

  // Generate dates including cliff end date
  const dateGenerated = [startDate, cliffEndDate];

  // Calculate end time for cycle-based drops
  let endTime;
  if (lot.value.plan_function === "CYCLE") {
    endTime = startDate.add(
      lot.value.duration * lot.value.cycle + lot.value.cliff_duration,
      timeUnit
    );
  } else {
    endTime = endDate;
  }

  const remainingPoints = totalPoints - 1;
  const intervalAfterCliff = endTime.diff(cliffEndDate) / (remainingPoints - 1);

  for (let i = 1; i < remainingPoints; i++) {
    dateGenerated.push(cliffEndDate.add(intervalAfterCliff * i, "millisecond"));
  }

  const xAxis = dateGenerated.map((date) => date.format("DD-MM"));
  const yAxis = [];
  const maxTokens = divideNumberUsingDecimals(lot.value.total_listing, props.currentToken.decimal).toString();

  // Only two points for cliff period
  yAxis.push(0); // Start point
  yAxis.push(0); // Cliff end point

  if (lot.value.plan_function === "CYCLE") {
    // Calculate drops for cycle-based vesting
    const dropPerCycle = maxTokens / lot.value.cycle;
    let amount = 0;

    // Calculate exact time between drops based on total duration and number of cycles
    const totalVestingTime = endTime.diff(cliffEndDate);
    const timeBetweenDrops = totalVestingTime / lot.value.cycle;

    for (let i = 0; i < remainingPoints - 1; i++) {
      const timeFromCliff = dateGenerated[i + 2].diff(cliffEndDate);
      // Only start counting completed cycles after cliff period
      if (timeFromCliff < 0) {
        amount = 0;
      } else {
        const completedCycles = Math.floor(timeFromCliff / timeBetweenDrops);
        amount = Math.min(dropPerCycle * completedCycles, maxTokens);
      }
      yAxis.push(amount);
    }

    console.log("yAxis", yAxis);
  } else {
    // Original time-based linear vesting
    const stepsAfterCliff = totalPoints - 3;
    const stepSize = maxTokens / (stepsAfterCliff + 1);

    for (let i = 0; i < stepsAfterCliff; i++) {
      const amount = stepSize * (i + 1);
      yAxis.push(amount);
    }
  }

  // Explicitly set the last point to maxTokens
  yAxis.push(maxTokens);

  console.log("Final yAxis values:", yAxis);

  const cliffEndIndex = 1; // Now cliff ends at index 1

  const cliffLine = {
    type: "line",
    xMin: 0,
    xMax: cliffEndIndex,
    yMin: Math.min(...yAxis) + 0.1 * (Math.max(...yAxis) - Math.min(...yAxis)),
    yMax: Math.min(...yAxis) + 0.1 * (Math.max(...yAxis) - Math.min(...yAxis)),
    borderColor: "#ff6767",
    borderWidth: 1,
    tension: 0,
    borderDash: [5, 5],
    label: {
      content: "Cliff Period",
      backgroundColor: "#2e2333",
      display: true,
      position: "center",
      color: "#ff6767",
      padding: {
        right: 8,
        left: 8,
        top: 3,
        bottom: 1,
      },
      font: {
        size: 12,
        weight: "regular",
      },
      borderRadius: 3,
    },
  };

  const cliffLine2 = {
    type: "line",
    xMin: cliffEndIndex,
    xMax: cliffEndIndex,
    yMin: 0,
    yMax: Math.min(...yAxis) + 0.2 * (Math.max(...yAxis) - Math.min(...yAxis)),
    borderColor: "#ff6767",
    borderWidth: 1,
    tension: 0,
    borderDash: [5, 5],
  };

  const annotation = {
    ...(Number(lot.value.cliff_duration) > 0 && { 
      cliffLine, 
      cliffLine2 
    }),
  };

  console.log("xAxis", xAxis);

  const ctx = document.getElementById("myChart");
  const gradient = ctx.getContext("2d").createLinearGradient(0, 0, 0, 400);
  gradient.addColorStop(0, "rgba(80, 241, 135, 0.2)"); // Changed from -0.0122
  gradient.addColorStop(1, "rgba(80, 241, 135, 0)"); // Changed from 1.0611

  const dropDates = [];
  if (lot.value.plan_function === "CYCLE") {
    const dropPerCycle = maxTokens / lot.value.cycle;
    const totalVestingTime = endTime.diff(cliffEndDate);
    const timeBetweenDrops = totalVestingTime / lot.value.cycle;

    for (let i = 0; i < lot.value.cycle; i++) {
      const dropDate = dayjs(cliffEndDate).add(
        lot.value.duration * (i+1),
        timeUnit
      );
      // console.log(
      //   "dropDate",
      //   i,
      //   lot.value.duration * (i + 1),
      //   timeUnit,
      //   lot.value.duration,
      //   dropDate.format("DD-MM-YYYY")
      // );
      const amount = dropPerCycle * (i + 1);
      dropDates.push({ date: dropDate, amount: amount });
    }
  }

  console.log("dropDates", dropDates);

  chart.value = new $chart(ctx, {
    type: "line",
    data: {
      labels: xAxis,
      borderColor: "#FFFF",
      datasets: [
        {
          label: currentToken.value.token_ticker,
          data: yAxis,
          pointRadius: 0,
          // borderColor : ["rgba(232, 93, 93, 0.5)"],
          pointBackgroundColor: function (context) {
            const index = context.dataIndex;
            // Ensure point fill color follows the line color
            const color = index < cliffEndIndex ? "#FF6767" : "#50F187"; // Red before, Blue after
            return color;
          },
          pointBorderColor: function (context) {
            const index = context.dataIndex;
            // Ensure point border color follows the line color
            const color = index < cliffEndIndex ? "#FF6767" : "#50F187"; // Red before, Blue after
            return color;
          },

          segment: {
            borderColor: function (context) {
              const index = context.p0DataIndex;
              return index < cliffEndIndex
                ? "rgba(255, 255, 255, 0)"
                : "#50F187";
            },
            backgroundColor: function (context) {
              const index = context.p0DataIndex;
              return index < cliffEndIndex ? "rgba(232, 93, 93, 0)" : gradient;
            },
          },
          borderWidth: 2,
          fill: true,
          stepped: true,
        },
      ],
    },
    options: {
      responsive: true,
      maintainAspectRatio: false, // Disable aspect ratio to use fixed height
      scales: {
        y: {
          position: "left",
          grid: {
            color: "rgba(255, 255, 255, 0.2)",
          },
          border: {
            display: false, // Disable y-axis border line
          },
          ticks: {
            // precision: 0,
            // callback: function (value) {
            //   // Round to nearest power of 10 that maintains ~10 ticks
            //   const maxValue = Math.max(...this.chart.data.datasets[0].data);
            //   const step = Math.pow(10, Math.floor(Math.log10(maxValue / 10)));
            //   return numeral(Math.round(value / step) * step).format("0,0");
            // },
            // count: 8,
            padding: 8,
          },
        },
        x: {
          title: {
            display: true,
            text: "Vesting Duration", // Add your title here
            align: "center",
            color: "#84858E",
            padding: { top: 20 },
          },
          ticks: {
            maxTicksLimit: 15, // Limits the number of visible x-axis labels
          },
          grid: {
            display: false, // Disable x-axis grid lines
          },
        },
      },

      // responsive: true,
      animation: {
        onComplete: function (e) {
          isChartReady.value = true;
          // drawVerticalLine(chart.value, cliffEndIndex);
        },
      },
      plugins: {
        annotation: {
          annotations:
            // line1: {
            //   type: "line",
            //   yMin: 60,
            //   yMax: 60,
            //   borderColor: "rgb(255, 99, 132)",
            //   borderWidth: 2,
            // },
            annotation,
        },
        legend: {
          display: false,
          position: "top",
        },
        tooltip: {
          mode: "index",
          intersect: false,
          callbacks: {
            title: function (tooltipItems) {
              console.log("tooltipItems", tooltipItems);
              const index = tooltipItems[0].dataIndex;
              const currentValue = tooltipItems[0].raw;

              if (lot.value.plan_function === "CYCLE" && index > 1) {
                console.log("yAxis", yAxis, tooltipItems, dropDates);

                const previousValue = yAxis[index - 1];
                const currentValue = yAxis[index];

                if (currentValue > previousValue) {
                  const dropInfo = dropDates.find(
                    (d) => d.amount === currentValue
                  );
                  if (dropInfo) {
                    return `Drop Date: ${dropInfo.date.format("DD-MM-YYYY")}`;
                  }
                }
              }
              return dateGenerated[index].format("DD-MM-YYYY");
            },
          },
        },
        fixedLine: {
          yPosition: 30, // Fixed y-axis value
          color: "rgba(0, 255, 0, 1)", // Line color (green)
        },
      },
    },
  });

  console.log(chart.value);
}
</script>

<style></style>

<template>
  <div
    v-if="!hasAccess && lot.is_private"
    id="buyComponent"
    class="bg-white h-full bg-opacity-[6%] md:min-h-[533px] lg:min-h-[537px] px-5 py-6 md:px-5 md:py-6 lg:p-8 rounded-[12px] font-medium"
  >
    <div class="mt-[122px] mb-[102px]">
      <div class="width-full flex justify-center mb-8">
        <div
          class="flex items-center justify-center bg-[#2f2331] w-[80px] h-[80px] rounded-full"
        >
          <inline-svg :src="lockRedIcon" class="w-[48px] h-[48px]"></inline-svg>
        </div>
      </div>

      <h5
        class="text-center text-white text-[24px] leading-[22px] font-satoshiBlack mb-6"
      >
        VIP Lot Access Required
      </h5>

      <div v-if="!account.isConnected">
        <div class="flex justify-center">
          <button
            class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 !font-medium disabled:opacity-20 disabled:bg-primary disabled:text-base-100"
            @click="showConnectModal"
          >
            Connect your wallet
          </button>
        </div>
      </div>
      <div v-if="account.isConnected && !hasAccess">
        <h5
          class="text-center text-white/50 text-[14px] leading-5 font-satoshiMedium mb-8"
        >
          This is a VIP lot. You need to request access to trade.
        </h5>

        <div class="flex justify-center">
          <button
            class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 !font-medium disabled:opacity-20 disabled:bg-primary disabled:text-base-100"
            @click="requestPrivateLotAccess"
          >
            Request Access
          </button>
        </div>
      </div>
    </div>
  </div>
  <div
    v-else
    class="bg-white h-full bg-opacity-[6%] md:min-h-[533px] lg:min-h-[537px] px-5 py-6 md:px-5 md:py-6 lg:p-8 rounded-[12px] font-medium"
  >
    <div class="flex items-center justify-between mb-7">
      <h3 class="text-xl md:text-base">
        Buy LOT
        {{ common.formatLotId(lot.token_ticker, lot.display_id, lot.list_id) }}
      </h3>
      <div
        class="bg-white text-input-icons bg-opacity-[8%] text-2sm p-3 py-[3px] rounded-[4px] font-medium"
      >
        {{ common.listType[lot.listing_type] }} Fill
      </div>
    </div>
    <form @submit.prevent="buyConfirm">
      <div class="flex flex-col gap-2">
        <div class="flex justify-between text-sm text-input-icons">
          <div class="flex items-center">
            <inline-svg
              :src="inputIcon"
              class="w-[14px] h-[14px] md:w-4"
            ></inline-svg>
            <h6 class="text-sm text-white md:text-2sm">Input</h6>
          </div>

          <div class="flex flex-row items-center gap-2">
            <inline-svg
              class="stroke-input-icons"
              width="12"
              height="12"
              :src="walletIcon"
            ></inline-svg>
            <div>
              {{ formatUSDT(props.usdtBalance) }}
              USDT
            </div>
          </div>
        </div>
        <div class="flex flex-col gap-0.5 relative mb-7">
          <label
            class="input h-[48px] flex items-center gap-2 mb-5"
            :class="
              buyForm.input > props.usdtBalance
                ? 'border input-error'
                : 'border border-textbox-border'
            "
          >
            <img
              class="w-[20px] h-[20px] rounded-full coin-border"
              :src="usdtToken"
              alt="token-img"
            />
            <input
              type="number"
              class="w-full grow read-only:opacity-50"
              :step="USDT_STEP"
              required
              v-model="buyForm.input"
              min="1"
              :readonly="lot.listing_type == 1"
              @input="handleInputChange($event.target.value)"
              @keypress="(e) => common.handleNumberInput(e, 6)"
              :disabled="isUpdatingSlider"
            />
            <span @click="maxInput" class="text-sm cursor-pointer text-primary"
              >MAX</span
            >
          </label>
          <h3
            v-if="buyForm.input > props.usdtBalance"
            class="text-sm text-error absolute top-[50px]"
          >
            Not enough USDT
          </h3>

          <div
            v-if="lot.listing_type != 1"
            class="relative flex flex-col items-center mt-3 mb-1"
          >
            <!-- Range Slider -->
            <input
              :disabled="lot.listing_type == 1"
              type="range"
              v-model="sliderValue"
              :min="minValue"
              :max="maxValue"
              step="0.0001"
              @input="updatePercentage"
              id="slider"
              ref="slider"
              class="relative z-10 w-full h-1 rounded-lg appearance-none cursor-pointer slider bg-range-grey"
            />
            <div class="w-full flex justify-between absolute -top-0.5 z-0">
              <div class="w-2 h-2"></div>
              <div
                class="w-2 h-2 rounded-full"
                :class="percentage > 25 ? 'bg-range-blue' : 'bg-range-grey'"
              ></div>
              <div
                class="w-2 h-2 rounded-full"
                :class="percentage > 50 ? 'bg-range-blue' : 'bg-range-grey'"
              ></div>
              <div
                class="w-2 h-2 rounded-full"
                :class="percentage > 75 ? 'bg-range-blue' : 'bg-range-grey'"
              ></div>
              <div class="w-2 h-2"></div>
            </div>
            <!-- Slider Value and Percentage -->
            <!-- <div class="text-center">
                  <p class="text-lg font-semibold">Slider Value: {{ sliderValue }}</p>
                  <p class="text-lg font-semibold">Percentage: {{ percentage }}%</p>
                </div> -->
          </div>
          <!-- <div
            v-if="lot.discount_type == 0"
            class="relative flex flex-col items-center mt-3 mb-1"
          >
            <input
              type="range"
              disabled
              value="0"
              class="relative z-10 w-full h-1 rounded-lg appearance-none cursor-pointer bg-range-grey"
            />
            <div class="w-full flex justify-between absolute -top-0.5 z-0">
              <div class="w-2 h-2"></div>
              <div class="w-2 h-2 rounded-full bg-range-grey"></div>
              <div class="w-2 h-2 rounded-full bg-range-grey"></div>
              <div class="w-2 h-2 rounded-full bg-range-grey"></div>
              <div class="w-2 h-2"></div>
            </div>
          </div> -->
          <div
            v-if="lot.discount_type == 1"
            class="flex justify-between mt-1 text-sm text-primary"
          >
            <h5>Min {{ numeral(minValue).format("0,0", Math.floor) }}%</h5>
            <h5>Max {{ numeral(maxValue).format("0,0", Math.floor) }}%</h5>
          </div>
        </div>
      </div>
      <div class="relative flex flex-col gap-2 mb-6 text-sm">
        <div class="flex justify-between text-input-icons">
          <div class="flex items-center">
            <inline-svg
              :src="inputIcon"
              class="rotate-180 w-[14px] h-[14px] md:w-4 md:h-4"
            ></inline-svg>
            <h6 class="text-sm text-white md:text-2sm">Output</h6>
          </div>
          <div class="flex flex-row items-center gap-2">
            <inline-svg
              class="stroke-input-icons"
              width="12"
              height="12"
              :src="walletIcon"
            ></inline-svg>
            <div>
              {{
                formatUSDT(
                  divideNumberUsingDecimals(
                    lot.remaining_listing,
                    currentToken.decimal
                  )
                )
              }}
              {{ lot.token_ticker }}
            </div>
          </div>
        </div>
        <label
          class="input input-bordered h-[48px] flex items-center gap-2"
          :class="
            toBigNumber(buyForm.output).isGreaterThan(
              divideNumberUsingDecimals(
                lot.remaining_listing,
                currentToken.decimal
              )
            )
              ? 'border input-error'
              : 'border border-textbox-border'
          "
        >
          <img
            :src="currentToken.token_image"
            alt="token-img"
            class="w-[20px] h-[20px] rounded-full coin-border"
          />
          <input
            type="number"
            class="w-full grow read-only:opacity-50"
            :step="TOKEN_STEP"
            required
            v-model="buyForm.output"
            :readonly="lot.listing_type == 1"
            @input="handleOutputChange($event.target.value)"
            @keypress="(e) => common.handleNumberInput(e, 6)"
            :disabled="isUpdatingSlider"
          />
          <span @click="maxInput" class="text-sm cursor-pointer text-primary"
            >MAX</span
          >
        </label>
        <div class="flex justify-between mt-1">
          <h3 class="text-xs font-medium">
            1 {{ lot.token_ticker }} =
            {{
              divideNumberUsingDecimals(
                lot.best_price ?? lot.listed_price,
                USDT_DECIMALS
              )
            }}
            USDT
          </h3>
          <h3
            v-if="
              toBigNumber(buyForm.output).isGreaterThan(
                divideNumberUsingDecimals(
                  lot.remaining_listing,
                  currentToken.decimal
                )
              )
            "
            class="text-xs text-error"
          >
            Not enough {{ lot.token_ticker }}
          </h3>
        </div>
      </div>
      <div
        class="flex flex-col justify-center bg-modal-textbox rounded-[8px] text-sm p-4 px-5 h-[90px] gap-[14px] mb-6 text-white/60"
      >
        <div
          class="flex justify-between"
          :class="lot.discount_type == 0 ? 'text-white/60' : 'text-success'"
        >
          <span class="flex items-center">
            <div>{{ discountType[lot.discount_type] }} Discount</div>
            <!-- <span v-if="lot.max_discount * 1 > 0"
                  >(-{{
                    numeral(buyForm.discountPct * 100).format(
                      "0,0.00",
                      Math.floor
                    )
                  }}%)</span
                > -->
            <div
              v-if="lot.discountType == 1"
              class="relative inline-block pl-1 pr-2 group"
            >
              <inline-svg
                :src="infoIcon"
                class="w-3 h-3 stroke-input-icons"
              ></inline-svg>
              <div
                class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg"
              >
                <div class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll">
                  The linear discount allows you to receive up to
                  {{ formatUSDT(buyForm.discountDeduct) }}% off, proportional to
                  the amount of the total supply you purchase. The more you buy,
                  the greater the discount, reaching the full 5% at 100% of the
                  supply.
                </div>

                <!-- Triangle pointer -->
                <div
                  class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-b border-l -left-1 top-1/2 bg-modal-textbox border-textbox-border"
                ></div>
              </div>
            </div>
          </span>
          <span class="flex items-center gap-1">
            <div>
              {{ formatUSDT(buyForm.discountDeduct) }}
            </div>
            <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
          </span>
        </div>
        <div class="flex justify-between text-white">
          <div>Cost</div>
          <div class="flex items-center gap-1">
            <div>
              {{
                formatUSDT(
                  divideNumberUsingDecimals(
                    lot.best_price ?? lot.listed_price,
                    USDT_DECIMALS
                  ).multipliedBy(buyForm.output)
                )
              }}
            </div>
            <img class="w-[14px] h-[14px]" :src="usdtToken" alt="token-img" />
          </div>
        </div>
      </div>
      <div class="flex justify-center">
        <button
          class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 !font-medium disabled:opacity-20 disabled:bg-primary disabled:text-base-100"
          :disabled="
            buyForm.input > props.usdtBalance ||
            toBigNumber(buyForm.output).isGreaterThan(
              divideNumberUsingDecimals(
                lot.remaining_listing,
                currentToken.decimal
              )
            )
          "
        >
          Review Buy
        </button>
      </div>
    </form>
    <!-- <NewModalBuySummary
          :lot="props.lot"
          :buyForm="buyModal?.buyForm"
          :isMarketplaceBuy="true"
          ref="newBuySummaryModal"
      ></NewModalBuySummary> -->
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import walletIcon from "~/assets/images_new/icons/buy-wallet.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import inputIcon from "~/assets/images_new/icons/input.svg";
import infoIcon from "~/assets/images_new/icons/info.svg";
import { USDT_DECIMALS, TOKEN_STEP, USDT_STEP } from "~/utils/const";
import {
  divideNumberUsingDecimals,
  formatUSDT,
  toBigNumber,
} from "~/utils/number";

import lockRedIcon from "~/assets/images_new/icons/lock-red.svg";
import { useAppKitAccount } from "@reown/appkit/vue";
import api from "~/utils/api";

import { ref } from "vue";

const props = defineProps({
  lot: Object,
  currentToken: Object,
  usdtBalance: Object,
});

const newWeb3Store = useNewWeb3Store();
const { isLoggedIn } = storeToRefs(newWeb3Store);
const connectModal = ref(null);
const account = useAppKitAccount();
const isUpdatingSlider = ref(false);

const web3Store = useWeb3Store();
const { refetch } = useMarketplace();

const hasAccess = ref(null);
const currentLot = ref(null);

const buyForm = reactive({
  input: null,
  output: null,
  discountDeduct: 0,
  discountPct: 0,
  platformFee: 0,
  received: 0,
  fullAmount: 0,
  payAmount: 0,
});

const discountType = {
  0: "No",
  1: "Linear",
  2: "Fixed",
};

const emit = defineEmits(["openBuySummary", "showAccessGranted"]);

//slider
const sliderValue = ref(0);
const minValue = ref(0);
const maxValue = ref(30);
const percentage = ref(0);
const slider = ref(null);

function showConnectModal() {
  connectModal.value?.handleClick?.();
  document.getElementById("connectModal").checked = true;
}

async function requestPrivateLotAccess() {
  if (account.value.isConnected && !isLoggedIn.value) {
    showConnectModal();
    return;
  } else {
    try {
      const res = await api.apiCall(
        "POST",
        "/private-lot/grant-access",
        {
          listId: currentLot.value.list_id,
          planId: currentLot.value.plan_id,
        }
      );

      if (res.data.status === "success") {
        emit("showAccessGranted");
        hasAccess.value = true;
      } else {
        useSweetAlertStore().showAlert("Error", "Access denied!", "error");
      }
    } catch (err) {
      console.log("Error access", err);
      useSweetAlertStore().showAlert(
        "Error",
        err?.message || "Request failed",
        "error"
      );
    }
  }
}

// web3Store.$subscribe(async (mutation, state) => {
//   console.log("web3 state", state.isInit);

//   if (state.isInit) {
//     usdtBalance.value = await web3Store.getTokenBalance(
//       useRuntimeConfig().public.usdtAddress
//     );
//   }
// });

async function handleInputChange(newValue) {
  if (isUpdatingSlider.value) return;

  try {
    await refetch();

    if (!newValue || newValue === "" || newValue === "0" || isNaN(newValue)) {
      buyForm.fullAmount = 0;
      buyForm.received = 0;
      buyForm.output = null;

      percentage.value = 0;
      sliderValue.value = 0;
      updateSliderAppearance();

      calculateFeesAndDiscount(0);
      return;
    }

    const rawPrice =
      currentLot.value.best_price ?? currentLot.value.listed_price;
    const price = divideNumberUsingDecimals(rawPrice, USDT_DECIMALS);

    if (price.isZero() || price.isNaN() || !price.isFinite()) {
      alert("Price is invalid");
      return;
    }

    buyForm.fullAmount = roundToPrecision(
      toBigNumber(newValue).dividedBy(price).toNumber()
    );
    buyForm.received = buyForm.fullAmount;
    buyForm.output = buyForm.received;

    const maxPossibleOutput = divideNumberUsingDecimals(
      currentLot.value.remaining_listing,
      props.currentToken.decimal
    );

    const calculatedPercentage = toBigNumber(buyForm.output)
      .dividedBy(maxPossibleOutput)
      .multipliedBy(100)
      .toNumber();

    percentage.value = Math.min(calculatedPercentage, 100);
    sliderValue.value = toBigNumber(percentage.value)
      .multipliedBy(maxValue.value)
      .dividedBy(100)
      .toNumber();

    updateSliderAppearance();
    calculateFeesAndDiscount(newValue);
  } catch (err) {
    console.error("Input change error:", err);
  }
}

async function handleOutputChange(newValue) {
  if (isUpdatingSlider.value) return;

  try {
    await refetch();

    if (!newValue || newValue === "" || newValue === "0" || isNaN(newValue)) {
      buyForm.input = null;
      buyForm.received = 0;
      percentage.value = 0;
      sliderValue.value = 0;
      updateSliderAppearance();
      calculateFeesAndDiscount(0);
      return;
    }

    const bestPrice = divideNumberUsingDecimals(
      currentLot.value.best_price ?? currentLot.value.listed_price,
      USDT_DECIMALS
    );

    const inputValue = roundToPrecision(
      toBigNumber(newValue).multipliedBy(bestPrice).toNumber()
    );

    buyForm.input = inputValue;
    buyForm.received = buyForm.output;

    const maxPossibleOutput = divideNumberUsingDecimals(
      currentLot.value.remaining_listing,
      props.currentToken.decimal
    );

    const calculatedPercentage = toBigNumber(newValue)
      .dividedBy(maxPossibleOutput)
      .multipliedBy(100)
      .toNumber();

    percentage.value = Math.min(calculatedPercentage, 100);
    sliderValue.value = toBigNumber(percentage.value)
      .multipliedBy(maxValue.value)
      .dividedBy(100)
      .toNumber();

    updateSliderAppearance();
    calculateFeesAndDiscount(inputValue);
  } catch (err) {
    console.error("Output change error:", err);
  }
}

function calculateFeesAndDiscount(inputValue) {
  const totalListing = divideNumberUsingDecimals(
    currentLot.value.total_listing,
    props.currentToken.decimal
  );
  const discountPct =
    currentLot.value.max_discount ?? currentLot.value.discount_pct / 100;
  const buyerFee = currentLot.value.buy_fee ?? currentLot.value.buyer_fee / 100;

  if (currentLot.value.discount_type == 1) {
    // Linear
    const discount = toBigNumber(buyForm.fullAmount)
      .dividedBy(totalListing)
      .multipliedBy(discountPct)
      .toNumber();
    buyForm.discountDeduct = roundToPrecision(
      toBigNumber(inputValue).multipliedBy(discount).toNumber()
    );
    buyForm.discountPct = roundToPrecision(discount);
  } else if (currentLot.value.discount_type == 2) {
    buyForm.discountDeduct = roundToPrecision(
      toBigNumber(inputValue).multipliedBy(discountPct).toNumber()
    );
    buyForm.discountPct = inputValue == 0 ? 0 : discountPct;
  } else {
    buyForm.discountDeduct = 0;
    buyForm.discountPct = 0;
  }

  buyForm.platformFee = roundToPrecision(
    toBigNumber(inputValue)
      .minus(buyForm.discountDeduct)
      .multipliedBy(buyerFee)
      .toNumber()
  );

  buyForm.payAmount = roundToPrecision(
    toBigNumber(inputValue)
      .minus(buyForm.discountDeduct)
      .plus(buyForm.platformFee)
      .toNumber()
  );
}

function roundToPrecision(value, precision = 6) {
  return parseFloat(value.toFixed(precision));
}

function updateOutput(lot) {
  if (currentLot.value.listing_type == 1) {
    buyForm.output = divideNumberUsingDecimals(
      lot.remaining_listing,
      props.currentToken.decimal
    ).toString();
    maxValue.value = 100;
    sliderValue.value = 100;
    percentage.value = 100;
    handleOutputChange(buyForm.output);
    // handleInputChange(buyForm.input);
  } else {
    buyForm.input = null;
    buyForm.output = null;
  }
}

function maxInput() {
  if (
    toBigNumber(props.usdtBalance)
      .dividedBy(
        divideNumberUsingDecimals(currentLot.value.listed_price, USDT_DECIMALS)
      )
      .isGreaterThan(
        divideNumberUsingDecimals(
          currentLot.value.remaining_listing,
          props.currentToken.decimal
        )
      )
  ) {
    buyForm.output = divideNumberUsingDecimals(
      currentLot.value.remaining_listing,
      props.currentToken.decimal
    ).toString();
    handleOutputChange(buyForm.output);
  } else {
    buyForm.input = numeral(props.usdtBalance).format("0.00", Math.floor);
    handleInputChange(buyForm.input);
  }
}

async function buyConfirm() {
  const config = web3Store.getConfigByCurrentNetwork();
  const tokenBalance = await web3Store.getTokenBalance(
    config.usdtToken,
    config.network.nativeCurrency.symbol
  );

  if (Number(buyForm.payAmount) > Number(tokenBalance)) {
    useSweetAlertStore().showAlert("Error", "Insufficient Token", "error");
    return;
  } else if (
    toBigNumber(buyForm.output).isGreaterThan(
      divideNumberUsingDecimals(
        currentLot.value.remaining_listing,
        props.currentToken.decimal
      )
    )
  ) {
    useSweetAlertStore().showAlert("Error", "Exceed maximum amount", "error");
    return;
  }

  document.getElementById("newBuySummaryModal").checked = true;
  emit("openBuySummary");
}

const updatePercentage = async () => {
  isUpdatingSlider.value = true;

  try {
    await refetch();

    percentage.value = (sliderValue.value / maxValue.value) * 100;

    const maxPossibleOutput = divideNumberUsingDecimals(
      currentLot.value.remaining_listing,
      props.currentToken.decimal
    );

    const calculatedOutput = maxPossibleOutput
      .multipliedBy(percentage.value)
      .dividedBy(100);

    buyForm.output = calculatedOutput.toString();

    const bestPrice = divideNumberUsingDecimals(
      currentLot.value.best_price ?? currentLot.value.listed_price,
      USDT_DECIMALS
    );

    const calculatedInput = calculatedOutput.multipliedBy(bestPrice);
    buyForm.input = roundToPrecision(calculatedInput.toNumber());

    buyForm.fullAmount = roundToPrecision(calculatedOutput.toNumber());
    buyForm.received = buyForm.fullAmount;

    calculateFeesAndDiscount(buyForm.input);

    updateSliderAppearance();
  } catch (err) {
    console.error("Slider update error:", err);
  } finally {
    setTimeout(() => {
      isUpdatingSlider.value = false;
    }, 100);
  }
};

function updateSliderAppearance() {
  nextTick(() => {
    const sliderElement = document.getElementById("slider");
    if (sliderElement) {
      sliderElement.style.background = `linear-gradient(to right, #0C4CAC ${percentage.value}%, #2B2D3D ${percentage.value}%)`;
    }
  });
}

onMounted(async () => {
  currentLot.value = props.lot;
  updateOutput(props.lot);
  hasAccess.value = props.lot.has_access;

  sliderValue.value = props.lot.listing_type == 1 ? 100 : 0;
  if (props.lot.listing_type == 1) {
    percentage.value = 100;
  } else {
    maxValue.value = props.lot.discount_pct;
  }

  updateSliderAppearance();
});

watch([sliderValue, percentage], () => {
  updateSliderAppearance();
});

defineExpose({
  updateOutput,
  buyForm,
});
</script>

<style scoped>
.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}

.slider::-moz-range-thumb {
  -webkit-appearance: none;
  background: #ffffff;
  border-radius: 100%;
  width: 12px;
  height: 12px;
}
</style>

<template>
  <div>
    <!-- Listing Sub-tabs -->
    <div class="flex gap-2 pb-8 text-2sm lg:border-b border-textbox-border">
      <div
        :class="{
          'relative cursor-pointer px-5': true,
          'tab-active': listingTab === 'lockedTokens',
          'text-white/50': listingTab !== 'lockedTokens',
        }"
        @click="listingTab = 'lockedTokens'"
      >
        Locked Tokens
      </div>
      <div
        :class="{
          'relative cursor-pointer px-5': true,
          'tab-active': listingTab === 'airdrop',
          'text-white/50': listingTab !== 'airdrop',
        }"
        @click="listingTab = 'airdrop'"
      >
        Airdrop
      </div>
    </div>

    <div v-if="listingTab === 'lockedTokens'">
      <!-- Desktop Table -->
      <div class="hidden lg:block mb-10 md:mb-[72px]">
        <TableMyAssets
          @showClaimEvent="$emit('showClaimEvent', $event.plan.vesting_address)"
          @showListEvent="$emit('showListEvent', $event.plan)"
          @showSummaryEvent="$emit('showSummaryEvent', $event.plan)"
          :tokenPlans="tokenPlans"
          :claimableAmounts="claimableAmounts"
        />
      </div>

      <!-- Mobile Cards -->
      <div class="lg:hidden mb-10 md:mb-[72px]">
        <div v-if="!tokenPlans || tokenPlans.length == 0">
          <NoData :redirectButton="true" content="No Asset Found"></NoData>
        </div>
        <div v-for="(token, index) in tokenPlans" :key="index" class="pb-2">
          <CardAsset
            @claimEvent="$emit('claimEvent', $event.plan.vesting_address)"
            @listEvent="$emit('listEvent', $event.plan)"
            @summaryEvent="$emit('summaryEvent', $event.plan)"
            :token="token"
            :claimableAmounts="claimableAmounts"
            :index="index"
          />
        </div>
      </div>
    </div>

    <div v-if="listingTab === 'airdrop'">
      <Airdrop :network="network" />
    </div>
  </div>
</template>

<script setup>
import Airdrop from "./airdrop/index.vue";

const props = defineProps({
  network: {
    type: String,
  },
  tokenPlans: {
    type: Array,
    default: () => [],
  },
  claimableAmounts: {
    type: Array,
    default: () => [],
  },
  userSummary: {
    type: Object,
    default: () => ({}),
  },
  claimableAmountSum: {
    type: [String, Number],
    default: 0,
  },
  formatUSDT: {
    type: Function,
    required: true,
  },
  toBigNumber: {
    type: Function,
    required: true,
  },
});

const emit = defineEmits([
  "claimEvent",
  "listEvent",
  "summaryEvent",
  "showClaimEvent",
  "showListEvent",
  "showSummaryEvent",
]);

const listingTab = ref("lockedTokens");
</script>

<style scoped>
/* Add any specific styles for the my assets tab */
</style>

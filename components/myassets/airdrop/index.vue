<template>
	<MyAssetsAirdropWeb :network="network" :nightEligibleAmount="nightEligibleAmount" class="hidden lg:block" />
	<MyAssetsAirdropMobile :network="network" :nightEligibleAmount="nightEligibleAmount" class="lg:hidden" />
</template>

<script setup>
import MyAssetsAirdropWeb from "./web.vue";
import MyAssetsAirdropMobile from "./mobile.vue";
import { useAppKitAccount } from "@reown/appkit/vue";
import { divideNumberUsingDecimals } from "~/utils/number"

const account = useAppKitAccount()
const accountAddress = computed(() => account.value.address)
const nightEligibleAmount = ref(null)

const props = defineProps({
	network: {
		type: String,
	},
});


watch(accountAddress, async () => {
	if (!accountAddress) return
	try {
		const checkEligibilityResponse = await fetch(`${useRuntimeConfig().public.apiUrl}/midnight/eligibility/${accountAddress.value}?network=${props.network.toLowerCase()}`)
		const checkEligibilityResult = await checkEligibilityResponse.json()
		const resultEligibleAmount = checkEligibilityResult.message.amount
		if (typeof resultEligibleAmount === "number") {
			nightEligibleAmount.value = divideNumberUsingDecimals(resultEligibleAmount, 6)
		} else {
			nightEligibleAmount.value = "Error"
		}
	} catch (err) {
		nightEligibleAmount.value = "Error"
		console.error('err', err)
	}
})

</script>

<template>
  <div>
    <!-- Listing Sub-tabs -->
    <div class="flex gap-2 pb-8 text-2sm lg:border-b border-textbox-border">
      <div
        :class="{ 'relative cursor-pointer px-5': true, 'tab-active': listingTab === 'active', 'text-white/50': listingTab !== 'active' }"
        @click="listingTab = 'active'">Active Listing</div>
      <div
        :class="{ 'relative cursor-pointer px-5': true, 'tab-active': listingTab === 'orders', 'text-white/50': listingTab !== 'orders' }"
        @click="listingTab = 'orders'">Indications of interest</div>
    </div>

    <!-- Active Listing Content -->
    <div v-if="listingTab === 'active'">
      <div v-if="userListedLots.length > 0">
        <!-- Desktop Table -->
        <div class="hidden mt-2 lg:block">
          <TableLot :lots="userListedLots" :getCountDown="getCountDown" :getAllLots="getUserListedLots"
            :twfChangesInfo="twfChangesInfo" @showBuyModalEvent="delistConfirm($event.lot)"
            noDataContent="No Active Listings" :myAssets="true" :network="network" />
        </div>

        <!-- Pagination -->
        <Pagination @callback="$emit('handlePaginateUpdate')" :totalItems="userListedLotsMeta?.itemCount ?? 0"
          ref="paginateRef" />

        <!-- Mobile Cards -->
        <div v-for="(lot, index) in userListedLots" :key="index" class="pb-4 lg:hidden">
          <CardAllLot @showBuyModalEvent="delistConfirm($event.lot || lot)" :tokenImg="lot.token_image"
            :networkImg="lot.network_image" :networkSymbol="lot.network_symbol" :totalListing="lot.total_listing"
            :remainingListing="lot.remaining_listing ?? 0" :tokenName="lot.token_name" :tokenTicker="lot.token_ticker"
            :tokenDecimal="lot.token_decimal"
            :listId="lot.list_id" :listingType="lot.listing_type"
            :tokenPrice="divideNumberUsingDecimals(lot.token_price ?? 0, USDT_DECIMALS).toString()"
            :bestPrice="divideNumberUsingDecimals(lot.listed_price ?? 0, USDT_DECIMALS).toString()"
            :maxDiscount="lot.discount_pct" :unlockStart="lot.start_date" :displayId="lot.display_id"
            :twfHourChanges="`${twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol}${lot.twf_hour_changes.replace('-', '')}%`"
            :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color"
            :lotId="formatLotId(lot.token_ticker, lot.display_id, lot.list_id)" :buttonText="'Delist'" 
            :isPrivate="lot.is_private" />
        </div>
      </div>
      <div v-else>
        <NoData content="No Active Listings" />
      </div>
    </div>

    <!-- Indications of Interest Content -->
    <div v-if="listingTab === 'orders'">
      <MyAssetsListingOffers :network="network" />
    </div>
  </div>
</template>

<script setup>
import common from "~/utils/common";

const props = defineProps({
  network: {
    type: String,
    required: true
  },
  userListedLots: {
    type: Array,
    default: () => []
  },
  userListedLotsMeta: {
    type: Object,
    default: () => ({})
  },
  twfChangesInfo: {
    type: Object,
    default: () => ({})
  },
  getCountDown: {
    type: Function,
    required: true
  },
  getUserListedLots: {
    type: Function,
    required: true
  },
  delistConfirm: {
    type: Function,
    required: true
  },
  divideNumberUsingDecimals: {
    type: Function,
    required: true
  },
  USDT_DECIMALS: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['handlePaginateUpdate']);

const listingTab = ref('active');

// Use the common utility function for formatting lot ID
const formatLotId = (ticker, displayId, listId) => {
  return common.formatLotId(ticker, displayId, listId);
};
</script>

<style scoped>
/* Add any specific styles for the listing tab */
</style>

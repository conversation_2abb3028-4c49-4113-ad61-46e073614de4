<template>
  <div>
    <!-- Activity Content -->
    <div v-if="activities.length === 0" class="flex items-center justify-center min-h-[300px] md:min-h-[400px] px-4">
      <!-- Empty State -->
      <div class="flex flex-col gap-4 md:gap-6 items-center text-center max-w-sm">
        <!-- Activity Illustration -->
        <div class="relative">
          <div
            class="w-20 h-20 md:w-24 md:h-24 bg-gradient-to-br from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center relative overflow-hidden">
            <!-- Box illustration -->
            <div class="w-12 h-9 md:w-16 md:h-12 bg-blue-200/30 rounded-lg relative">
              <div class="absolute top-1 left-1 w-2 h-2 md:w-3 md:h-3 bg-white/40 rounded-full"></div>
              <div class="absolute top-1 right-1 w-2 h-2 md:w-3 md:h-3 bg-white/40 rounded-full"></div>
              <div
                class="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-6 h-0.5 md:w-8 md:h-1 bg-white/40 rounded">
              </div>
            </div>
            <!-- Floating dots -->
            <div
              class="absolute top-1.5 right-1.5 md:top-2 md:right-2 w-1.5 h-1.5 md:w-2 md:h-2 bg-red-400 rounded-full">
            </div>
            <div
              class="absolute bottom-1.5 left-1.5 md:bottom-2 md:left-2 w-1 h-1 md:w-1.5 md:h-1.5 bg-yellow-400 rounded-full">
            </div>
          </div>
        </div>

        <!-- No Activity Text -->
        <div>
          <h3 class="text-white text-lg md:text-xl font-medium mb-2">No Activity</h3>
          <p class="text-input-icons text-xs md:text-sm mb-4 md:mb-6">some text here for trading?</p>
        </div>

        <!-- Go to Marketplace Button -->
        <button @click="goToMarketplace"
          class="btn btn-primary text-2sm min-h-10 max-h-10 px-6 md:px-8 cursor-pointer hover:opacity-80 w-full sm:w-auto">
          Go to Marketplace
        </button>
      </div>
    </div>

    <!-- Activity List (for future implementation) -->
    <div v-else class="space-y-4">
      <div v-for="activity in activities" :key="activity.id" class="bg-white/[6%] rounded-xl p-4">
        <!-- Activity item content will be implemented when API is available -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center">
              <inline-svg :src="getActivityIcon(activity.type)" class="w-5 h-5 fill-primary"></inline-svg>
            </div>
            <div>
              <div class="text-white text-sm font-medium">{{ activity.title }}</div>
              <div class="text-input-icons text-xs">{{ activity.description }}</div>
            </div>
          </div>
          <div class="text-right">
            <div class="text-white text-sm">{{ activity.amount }}</div>
            <div class="text-input-icons text-xs">{{ activity.timestamp }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import tradeIcon from "~/assets/images_new/icons/trade.svg";
import claimIcon from "~/assets/images_new/icons/claim.svg";
import listIcon from "~/assets/images_new/icons/list.svg";

// Mock data structure for future implementation
const activities = ref([
  // Example structure for when API is available:
  // {
  //   id: 1,
  //   type: 'trade',
  //   title: 'Token Purchase',
  //   description: 'Bought 100 USDT worth of tokens',
  //   amount: '+100 USDT',
  //   timestamp: '2 hours ago'
  // },
  // {
  //   id: 2,
  //   type: 'claim',
  //   title: 'Token Claim',
  //   description: 'Claimed vested tokens',
  //   amount: '+50 TOKENS',
  //   timestamp: '1 day ago'
  // },
  // {
  //   id: 3,
  //   type: 'list',
  //   title: 'Token Listed',
  //   description: 'Listed tokens for sale',
  //   amount: '200 TOKENS',
  //   timestamp: '3 days ago'
  // }
]);

const goToMarketplace = () => {
  navigateTo("/");
};

const getActivityIcon = (type) => {
  switch (type) {
    case 'trade':
      return tradeIcon;
    case 'claim':
      return claimIcon;
    case 'list':
      return listIcon;
    default:
      return tradeIcon;
  }
};
</script>

<style scoped>
/* Add any specific styles for the activity tab */
</style>

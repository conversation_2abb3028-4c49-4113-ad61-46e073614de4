<template>
  <div class="pb-6 space-y-6 md:space-y-8">
    <!-- Next Unlock Section -->
    <div>
      <div class="relative p-4 overflow-hidden bg-[#191B2B] rounded-xl md:px-6 md:py-5">
        <!-- SANDBOX Card -->
        <div class="relative flex flex-col items-start gap-2">
          <div class="text-sm leading-4 text-white md:text-sm">Next Unblock</div>
          <div class="text-xl font-medium leading-9 text-white md:text-2xl">SANDBOX</div>
          <div class="z-10">
            <LinkTelegramPromptButton>
              <button class="text-xs leading-4 transition-colors text-primary md:text-sm hover:text-white">
                Get Notified →
              </button>
            </LinkTelegramPromptButton>
          </div>
          <img class="absolute -top-[20px] h-[124px] -left-[24px]" :src="sandboxTokenEffect" alt="token-img" />
          <img class="absolute -top-[20px] h-[124px] -right-[24px]" :src="sandboxToken" alt="token-img" />
        </div>
      </div>
    </div>

    <!-- Locked Section -->
    <div class="p-5 bg-white bg-opacity-[4%] rounded-lg">
      <div class="flex items-center justify-between">
        <h2 class="mb-4 text-sm leading-4 text-white md:text-sm lg:text-sm font-satoshiBlack md:mb-5">Locked</h2>
        <div class="mb-4 text-xs text-input-icons md:text-sm">Estimated APR: ****%</div>
      </div>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        <!-- Total Claimable Card -->
        <div class="bg-white/[6%] rounded-xl p-4 md:p-5 md:pb-3 flex flex-col">
          <inline-svg :src="moneyBagIcon" class="w-3 h-3 md:w-5 md:h-5"></inline-svg>
          <span class="pt-4 text-xs text-input-icons md:text-xs">Total Claimable</span>
          <div class="flex items-center gap-1 pt-2 text-lg font-bold text-white md:text-2xl">
            {{ formatUSDT(claimableAmountSum) }}
            <span class="ml-1 text-sm text-primary md:text-lg"><img class="w-[20px] h-[20px]" :src="usdtIcon"
                alt="token-img" />
            </span>
          </div>
        </div>

        <!-- Total Locked Card -->
        <div class="bg-white/[6%] rounded-xl p-4 md:p-5 md:pb-3 flex flex-col">
          <inline-svg :src="lockIcon" class="w-3 h-3 md:w-5 md:h-5"></inline-svg>
          <span class="pt-4 text-xs text-input-icons md:text-xs">Total Locked</span>
          <div class="flex items-center gap-1 pt-2 text-lg font-bold text-white md:text-2xl">
            {{ formatUSDT(toBigNumber(userSummary.net_worth).plus(toBigNumber(userSummary.locked_amount))) }}
            <span class="ml-1 text-sm text-primary md:text-lg"><img class="w-[20px] h-[20px]" :src="usdtIcon"
                alt="token-img" />
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Referral Section -->
    <div class="p-5 bg-white bg-opacity-[4%] rounded-lg">
      <div class="flex items-center justify-between">
        <h2 class="mb-4 text-sm leading-4 text-white md:text-sm lg:text-sm font-satoshiBlack md:mb-5">Referral</h2>
        <div class="mb-4 text-xs text-input-icons md:text-sm">ID: 12PXX07</div>
      </div>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6">
        <!-- Total Referred Card -->
        <div class="bg-white/[6%] rounded-xl p-4 md:p-5 md:pb-3 flex flex-col">
          <inline-svg :src="userPlusIcon" class="w-3 h-3 md:w-5 md:h-5"></inline-svg>
          <span class="pt-4 text-xs text-input-icons md:text-xs">Total Referral</span>
          <div class="flex items-center gap-1 pt-2 text-lg font-bold text-white md:text-2xl">
            <div class="text-lg font-bold text-white md:text-2xl">0</div>
          </div>
        </div>

        <!-- Total Earn Card -->
        <div class="bg-white/[6%] rounded-xl p-4 md:p-5 md:pb-3 flex flex-col">
          <inline-svg :src="coinsIcon" class="w-3 h-3 md:w-5 md:h-5"></inline-svg>
          <span class="pt-4 text-xs text-input-icons md:text-xs">Total Earn</span>
          <div class="flex items-center gap-1 pt-2 text-lg font-bold text-white md:text-2xl">
            0.00
            <span class="ml-1 text-sm text-primary md:text-lg"><img class="w-[20px] h-[20px]" :src="usdtIcon"
                alt="token-img" />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import lockIcon from "~/assets/images_new/icons/lock.svg";
import walletIcon from "~/assets/images_new/icons/wallet.svg";
import usersIcon from "~/assets/images_new/icons/users.svg";
import dollarIcon from "~/assets/images_new/icons/dollar.svg";
import usdtIcon from "~/assets/images_new/tokens/usdt_3x.webp";
import moneyBagIcon from "~/assets/images_new/icons/money-bag.svg";
import userPlusIcon from "~/assets/images_new/icons/user-plus.svg";
import coinsIcon from "~/assets/images_new/icons/coins.svg";
import sandboxToken from "~/assets/images_new/tokens/sandbox_banner.png";
import sandboxTokenEffect from "~/assets/images_new/tokens/sandbox_banner_effect.png";
import LinkTelegramPromptButton from "../LinkTelegramPromptButton.vue";

const props = defineProps({
  userSummary: {
    type: Object,
    default: () => ({})
  },
  claimableAmountSum: {
    type: [String, Number],
    default: 0
  },
  tokenPlans: {
    type: Array,
    default: () => []
  },
  formatUSDT: {
    type: Function,
    required: true
  },
  toBigNumber: {
    type: Function,
    required: true
  }
});
</script>

<style scoped>
/* Add any specific styles for the overview tab */
</style>

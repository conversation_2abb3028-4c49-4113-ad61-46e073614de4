<template>
  <div
    class="relative inline-block"
    @mouseenter="showTooltipWithDelay"
    @mouseleave="hideTooltipWithDelay"
  >
    <div class="cursor-pointer">
      <inline-svg :src="questionTooltipIcon"></inline-svg>
    </div>
    <div
      v-if="showTooltip"
      class="absolute opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[126px] 2xl:w-[200px] left-full top-1/2 -translate-y-1/2 ml-2 shadow-lg"
    >
      <div class="max-h-[100px] p-3 overflow-y-auto w-full">
        VIP Lots are special, invitation-only opportunities reserved for early
        supporters and strategic partners.
        <!-- <a href="https://www.secondswap.io/H75PZYBH-AA0-0?network=ETH" target="_blank" rel="noopener noreferrer" 
          class="text-blue-600 hover:underline">
          Learn more
        </a> -->
      </div>
      <div
        class="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-2 bg-modal-textbox border-l border-b border-textbox-border rotate-45"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import InlineSvg from "vue-inline-svg";
import questionTooltipIcon from "~/assets/images_new/icons/question-tooltip.svg";

const showTooltip = ref(false);
let hideTimeout = null;

function showTooltipWithDelay() {
  if (hideTimeout) {
    clearTimeout(hideTimeout);
    hideTimeout = null;
  }
  showTooltip.value = true;
}

function hideTooltipWithDelay() {
  hideTimeout = setTimeout(() => {
    showTooltip.value = false;
  }, 500);
}
</script>

<style></style>

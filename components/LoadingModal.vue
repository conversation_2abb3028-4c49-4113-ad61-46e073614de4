<template>
	<BlankModal :id="id">
		<div class="flex flex-col items-center">
			<div class='loading-animation'></div>
			<div class="mt-2 text-sm text-white/30">Loading</div>
			<div class="mt-6 text-xl font-medium text-white">{{ title }}</div>
			<div class="mt-4 text-sm text-white/50">{{ message }}</div>
		</div>
	</BlankModal>
</template>

<script setup>
import BlankModal from './BlankModal.vue';

const props = defineProps({
	id: String,
	title: String,
	message: String,
})

</script>

<style scoped></style>

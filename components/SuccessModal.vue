<template>
	<BlankModal :id="id">
		<div class="flex flex-col items-center">
			<svg width="80" height="78" viewBox="0 0 80 78" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path
					d="M70 36.2603V38.9296C69.9963 45.1864 67.9015 51.2744 64.028 56.2857C60.1545 61.2969 54.7098 64.9629 48.5061 66.7369C42.3023 68.5109 35.6718 68.2979 29.6034 66.1296C23.535 63.9613 18.354 59.9539 14.8329 54.7051C11.3118 49.4562 9.63937 43.2472 10.065 37.004C10.4907 30.7608 12.9916 24.8179 17.1948 20.0617C21.3981 15.3054 27.0784 11.9907 33.3886 10.6118C39.6988 9.23294 46.3008 9.8638 52.21 12.4103"
					stroke="#50F187" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
				<path d="M70 15.7012L40 44.7158L31 36.0201" stroke="#50F187" stroke-width="3" stroke-linecap="round"
					stroke-linejoin="round" />
			</svg>

			<div class="mt-6 text-xl font-medium text-white">{{ title }}</div>
			<div class="mt-4 text-sm text-white/50">{{ message }}</div>
			<button v-if="action" class="flex w-full h-10 gap-2 mt-6 font-medium rounded-lg btn btn-primary btn-sm"
				@click="action.onClick">{{ action.name }}</button>
		</div>
	</BlankModal>
</template>

<script setup lang="ts">
import BlankModal from './BlankModal.vue';

const props = defineProps<{
	id: string,
	title: string,
	message: string,
	action?: {
		name: string,
		onClick: () => void,
	}
}>()
</script>

<style scoped></style>

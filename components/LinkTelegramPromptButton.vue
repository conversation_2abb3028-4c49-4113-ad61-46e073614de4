<template>
	<button class="flex items-center justify-center text-sm font-medium text-primary" @click="showConfirmation()">
		<slot />
	</button>
	<BlankModal id="link_telegram_prompt_modal">
		<div class="flex flex-col items-center gap-6">
			<img :src="telegramImg" />
			<h3 class="text-lg text-xl font-medium text-white">Connect Telegram Account</h3>
			<div class="text-sm font-medium text-center text-white/50">Link your Telegram account to receive
				real-time
				updates,
				<span class="whitespace-nowrap">trade
					notifications,
					and personalized
					alerts directly in your </span> Telegram.
			</div>
			<form method="dialog" class="w-full">
				<link-telegram-button type="submit">
					<div class="flex w-full h-10 gap-2 font-medium rounded-lg btn btn-primary btn-sm">
						<img :src="telegramIcon" alt="telegram icon"></img>
						Connect Now
					</div>
				</link-telegram-button>
			</form>
		</div>
	</BlankModal>
</template>

<script setup>
import telegramImg from "assets/images_new/common/telegram.png"
import telegramIcon from "~/assets/images_new/icons/social_media/telegram2.svg";
import BlankModal from "./BlankModal.vue";

const emit = defineEmits(["onSuccess"])

const showConfirmation = () => {
	link_telegram_prompt_modal.showModal()
}

</script>

<style scoped></style>

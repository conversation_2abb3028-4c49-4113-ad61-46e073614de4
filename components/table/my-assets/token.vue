<template>
  <tr v-for="(plan, planIndex) in token.plans" :key="plan.display_id"
    class="border-b border-textbox-border last:border-0 hover:bg-white hover:bg-opacity-[4%]">
    <td class="py-5">
      <div class="flex items-center gap-3">
        <div class="relative w-[44px] h-[44px] flex-shrink-0">
          <img class="w-full h-full rounded-full" :src="token.token_image" alt="token-img" />

          <img class="absolute bottom-0 w-[16px] h-[16px]" :src="token.network_image" alt="network-img" />
        </div>
        <div>
          <h4>{{ token.token_ticker }}</h4>
          <h5 class="text-sm text-input-icons">{{ token.token_name }}</h5>
        </div>
      </div>
    </td>
    <td class="text-primary">
      {{ plan.plan_name }}
    </td>
    <td>
      <span>
        {{
          formatToken(
            divideNumberUsingDecimals(plan.raw_amount, token.token_decimal)
          )
        }} {{ token.token_ticker }}
      </span>
    </td>

    <td v-if="Object.keys(claimableAmounts).length == 0">
      <div class="flex justify-center">
        <span class="loading loading-spinner loading-sm"></span>
      </div>
    </td>
    <td v-else>
      <span class="text-success">{{
        formatUSDT(
          claimableAmounts[plan.vesting_id]?.tokenAmount || 0
        )
      }}
      </span>
    </td>

    <td>
      {{ calcNextUnlock(plan) }}
    </td>
    <td>
      <h5>
        {{
          numeral(
            getSellable(
              divideNumberUsingDecimals(
                plan.total_allocated ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                plan.market_amount ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                plan.claimed_amount ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                plan.selling_limit ?? 0,
                token.token_decimal
              ).toString(),
              divideNumberUsingDecimals(
                plan.listed_amount ?? 0,
                token.token_decimal
              ).toString()
            )
          ).format("0,0.00")
        }}
      </h5>
    </td>
    <td>
      <div class="flex items-center gap-2">
        <button type="button" class="btn btn-outline btn-xs border-primary text-primary !rounded-2sm"
          :disabled="claimableAmounts[plan.vesting_id]?.tokenAmount * 1 == 0" @click="
            $emit('claimEvent', {
              plan: {
                ...plan,
                token_image: token.token_image,
              },
            })
            ">
          Claim
        </button>
        <button type="button" class="btn btn-outline btn-xs border-primary text-primary !rounded-2sm" :disabled="getSellable(
          divideNumberUsingDecimals(
            plan.total_allocated ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            plan.market_amount ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            plan.claimed_amount ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            plan.selling_limit ?? 0,
            token.token_decimal
          ).toString(),
          divideNumberUsingDecimals(
            plan.listed_amount ?? 0,
            token.token_decimal
          ).toString()
        ) <= 0" @click="
          $emit('listEvent', {
            plan: {
              ...plan,
              token_image: token.token_image,
              token_ticker: token.token_ticker,
              token_price: token.price,
              token_decimal: token.token_decimal,
              network_symbol: token.network_symbol,
            },
          })
          ">
          List
        </button>
        <button type="button" class="btn btn-outline btn-xs border-primary text-primary !rounded-2sm" @click="
          $emit('summaryEvent', {
            plan: {
              ...plan,
              token_image: token.token_image,
              token_ticker: token.token_ticker,
              token_price: token.price,
              token_decimal: token.token_decimal,
            },
          })
          ">
          Summary
        </button>
      </div>
    </td>
  </tr>
</template>

<script setup>
import numeral from "numeral";
import optionsIcon from "~/assets/images_new/icons/options.svg";
import dropdownIcon from "~/assets/images_new/icons/paginate-arrow.svg";
import InlineSvg from "vue-inline-svg";
import { TIME_UNIT, USDT_DECIMALS } from "~/utils/const";
import { divideNumberUsingDecimals, formatUSDT, formatToken } from "~/utils/number";

const { $dayjs } = useNuxtApp();
const timeUnit = TIME_UNIT;

const props = defineProps({
  token: Object,
  claimableAmounts: {
    type: Object,
    default: null,
  },
  showClaimEvent: Function,
  showListEvent: Function,
});

function calcNextUnlock(plan) {
  console.log("unlock plan", plan);
  const currentTime = $dayjs();

  if (plan.function == "TIME") {
    if (
      $dayjs(plan.start_date)
        // .add(plan.cliff_duration, timeUnit)
        .isAfter(currentTime)
    ) {
      return $dayjs(plan.start_date)
        // .add(plan.cliff_duration, timeUnit)
        .format("YYYY-MM-DD");
    } else {
      return "Time-based";
    }
  } else {
    if ($dayjs(plan.start_date).isAfter(currentTime)) {
      return $dayjs(plan.start_date).format("YYYY-MM-DD");
    } else if ($dayjs(plan.end_date).isBefore(currentTime)) {
      return "---";
    } else {
      // Calculate cycle duration and completed cycles
      const cycleDuration = plan.duration; // duration in days
      const startDate = $dayjs(plan.start_date);
      const daysSinceStart = currentTime.diff(startDate, timeUnit);
      const completedCycles = Math.floor(daysSinceStart / cycleDuration);

      console.log("completedCycles", completedCycles);

      // Calculate next unlock time
      const nextUnlockTime = startDate.add(
        (completedCycles + 1) * cycleDuration,
        timeUnit
      );

      // Check if next unlock is after end date
      if (nextUnlockTime.isAfter($dayjs(plan.end_date))) {
        return "---";
      }

      return nextUnlockTime.format("YYYY-MM-DD");
    }
  }
}

function getSellable(
  totalAllocated,
  marketAmount,
  claimedAmount,
  sellLimit,
  listedAmount
) {
  const claimable = totalAllocated * 1 + marketAmount * 1 - claimedAmount * 1;

  console.log(
    "sellable amount",
    totalAllocated,
    marketAmount,
    claimedAmount,
    sellLimit,
    listedAmount,
    claimable
  );

  console.log({
    "claimable amount": claimable,
    "sell limit": sellLimit,
    "listed amount": listedAmount,
    "result 1": sellLimit * 1 - listedAmount * 1,
    "result 2": claimable,
  });

  console.log(
    "final return",
    claimable > sellLimit * 1 ? sellLimit * 1 : claimable
  );

  return claimable > sellLimit * 1 ? sellLimit * 1 : claimable;
}
</script>

<style lang="scss" scoped></style>

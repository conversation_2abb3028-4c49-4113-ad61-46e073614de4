<template>
	<div class="relative flex flex-col items-center justify-center gap-2">
		<!-- <div class="bg-white w-[8px] h-[8px] border-[8px] border-[#0C4CAC] rounded-full"></div> -->
		<div class="w-[14px] h-[14px] flex-shrink-0 rounded-full flex items-center justify-center" :class="{
			'bg-[#0C4CAC]': stepIndex === currentStep,
			'bg-[#FF6767]': failed === true,
			'bg-[#50F187]': stepIndex < currentStep,
			// 'opacity-30': step === 'pending',
			'bg-[#0A1F49]': currentStep < stepIndex,
		}">
			<div v-if="currentStep <= stepIndex" class="min-w-[6px] max-w-[6px] h-[6px] rounded-full"
				:class="currentStep < stepIndex ? 'bg-white/30' : 'bg-white'"></div>
			<inline-svg v-else-if="failed" :src="crossIcon" class="w-[10px]"></inline-svg>
			<inline-svg v-else-if="stepIndex < currentStep" :src="checkIcon" class="w-[10px]"></inline-svg>
		</div>
		<div class="absolute top-3 text-[12px] text-text-secondary text-center whitespace-nowrap">{{ label }}</div>
	</div>
</template>

<script setup>
const props = defineProps([
	"label",
	"stepIndex",
	"currentStep",
	"failed",
]);
import InlineSvg from "vue-inline-svg";
import checkIcon from "@/assets/images/step/check.svg";
import crossIcon from "@/assets/images/step/cross.svg";
</script>

<template>
	<button class="btn btn-sm btn-primary w-[374px] h-[40px] max-w-full" @click="handleOpenCreateOfferModal">
		Indicate Interest
	</button>
	<Modal :title="`Signal interest in ${tokenName}`" :open="openedCreateOfferModal" modal-body-class="md:w-[460px]"
		@on-close="handleCloseCreateOfferModal">
		<div v-if="errorMessage" role="alert" class="mb-8 text-sm rounded-lg alert alert-info warning-alert">
			{{ errorMessage }}
		</div>
		<form @submit.prevent="createOffer">
			<div class="flex flex-col gap-2">
				<div class="flex justify-between text-sm text-input-icons">
					<div class="flex items-center">
						<inline-svg :src="inputIcon"></inline-svg>
						<h6 class="text-sm text-white">Price</h6>
					</div>

					<div class="flex flex-row items-center gap-2">
						<inline-svg class="stroke-input-icons" width="12" height="12" :src="walletIcon"></inline-svg>
						<div>
							{{ formatUSDT(web3Store.userInfo.nativeBalance) }} USDT
						</div>
					</div>
				</div>
				<div class="flex flex-col gap-0.5 relative mb-1">
					<CurrencyInput v-model="form.price" :leftIcon="usdtTokenIcon" precision="6" @input="onPriceChange" />
				</div>
			</div>
			<div class="relative flex flex-col items-center mt-8 mb-6">
				<div class="flex w-full">
					<input type="range" step="2" v-model="form.slider_value" @input="onSliderChange" :min="0" :max="100"
						id="slider" ref="slider"
						class="z-10 relative w-[80%] h-1 rounded-lg appearance-none cursor-pointer slider bg-[#575C74]" />
					<div class="w-[20%] h-1 bg-[#2C2D3D]"></div>
				</div>
				<div class="flex w-full">
					<div class="relative w-[80%]">
						<div id="output" for="slider"
							:style="{ '--min': 0, '--max': 100, left: `${Math.max(form.slider_value, 15)}%` }"
							class="absolute bottom-0 z-10 -translate-x-1/2 -translate-y-full">
							<div
								class="relative w-full h-full px-2 py-1 rounded border border-textbox-border bg-[#141627] text-xs whitespace-nowrap">
								{{ form.price }} USDT ({{ numeral((100 - form.slider_value) / 2).format("0,0[.][00]", Math.floor) }}%)
								<div
									class="absolute bottom-0 w-2 h-2 rotate-45 -translate-x-1/2 translate-y-1/2 border-r border-b border-textbox-border left-1/2 bg-[#141627]">
								</div>
							</div>
						</div>
						<div class="relative w-80%">
							<div class="absolute -translate-x-1/2 -translate-y-full left-[99.2%] bottom-full">
								<div
									class="relative h-full px-2 py-1 rounded border border-textbox-border bg-[#141627] text-xs whitespace-nowrap">
									{{ props.spotPrice }} USDT
									<div
										class="absolute bottom-0 w-2 h-2 rotate-45 -translate-x-1/2 translate-y-1/2 border-r border-b border-textbox-border left-1/2 bg-[#141627]">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="w-full flex justify-between absolute -top-0.5 pointer-events-none">
					<div class="w-2 h-2"></div>
					<div class="w-2 h-2 rounded-full" :class="form.slider_value > 25 ? 'bg-range-blue' : 'bg-[#575C74]'"></div>
					<div class="w-2 h-2 rounded-full" :class="form.slider_value > 50 ? 'bg-range-blue' : 'bg-[#575C74]'"></div>
					<div class="w-2 h-2 rounded-full" :class="form.slider_value > 75 ? 'bg-range-blue' : 'bg-[#575C74]'"></div>
					<div class="w-2 h-2 rounded-full" :class="form.slider_value > 100 ? 'bg-range-blue' : 'bg-[#575C74]'"></div>
					<div class="w-2 h-2"></div>
				</div>
				<div class="w-full flex justify-between absolute -top-0.5 pointer-events-none z-10">
					<div class="w-2 h-2"></div>
					<div class="w-2 h-2"></div>
					<div class="w-2 h-2"></div>
					<div class="w-2 h-2"></div>
					<div class="relative w-2 h-2 bg-white rounded-full">
						<div class="absolute text-xs -translate-x-1/2 text-input-icons whitespace-nowrap left-1/2 top-[12px]">Spot
							price</div>
					</div>
					<div class="w-2 h-2"></div>
				</div>
				<div class="flex w-full mt-2 text-xs">
					<div class="text-primary">Max discount 50%</div>
				</div>
			</div>

			<div class="flex justify-between mb-6 text-sm text-white">
				<div class="flex items-center gap-1">
					<h6>Set Unlock Date</h6>
					<inline-svg :src="infoIcon" class="rotate-180"></inline-svg>
				</div>
				<div class="flex items-center gap-3 text-primary">
					<div @click="() => form.unlockDate = '3M'" :class="{
						'cursor-pointer': true,
						'text-sm text-primary badge badge-primary bg-opacity-20 border-opacity-20': form.unlockDate === '3M'
					}">3M</div>
					<div @click="() => form.unlockDate = '6M'" :class="{
						'cursor-pointer': true,
						'text-sm text-primary badge badge-primary bg-opacity-20 border-opacity-20': form.unlockDate === '6M'
					}">6M</div>
					<div @click="() => form.unlockDate = '1Y'" :class="{
						'cursor-pointer': true,
						'text-sm text-primary badge badge-primary bg-opacity-20 border-opacity-20': form.unlockDate === '1Y'
					}">1Y</div>
				</div>
			</div>
			<div class="relative flex flex-col gap-2 mb-8 text-sm">
				<div class="flex justify-between text-input-icons">
					<div class="flex items-center">
						<inline-svg :src="inputIcon" class="rotate-180"></inline-svg>
						<h6 class="text-sm text-white">Quantity</h6>
					</div>
				</div>
				<label class="input input-bordered h-[48px] flex items-center gap-2" :class="false
					? 'border input-error'
					: 'border border-textbox-border'
					">
					<img class="w-[20px] h-[20px]" :src="tokenIconUrl" alt="token-img" />
					<input type="number" class="grow read-only:opacity-50 focus:outline-none focus:shadow-none focus:ring-0"
						min="0" step="0.001" required v-model="form.quantity" />
				</label>
				<h3 v-if="false" class="absolute text-xs text-error -bottom-4">
					Inline error message
				</h3>
			</div>
			<div class="flex flex-col bg-modal-textbox rounded-[8px] text-sm p-4 gap-3 mb-6 text-input-icons">
				<div class="flex justify-between">
					<div class="flex items-center gap-1">
						<h6>Price</h6>
						<inline-svg :src="infoIcon" class="rotate-180"></inline-svg>
					</div>
					<div class="flex items-center gap-1">
						<div>
							{{
								numeral(form.price).format("0[.]00[0000]", Math.floor)
							}}
							({{ numeral((100 - form.slider_value) / 2).format("0,0[.][00]", Math.floor) }}%)
						</div>
						<img class="w-[14px] h-[14px]" :src="usdtTokenIcon" alt="token-img" />
					</div>
				</div>
				<div class="flex justify-between">
					<div class="flex items-center gap-1">
						<h6>Quantity</h6>
						<inline-svg :src="infoIcon" class="rotate-180"></inline-svg>
					</div>
					<div class="flex items-center gap-1">
						<div>
							{{
								numeral(form.quantity).format("0.00", Math.floor)
							}}
						</div>
						<img class="w-[14px] h-[14px]" :src="tokenIconUrl" alt="token-img" />
					</div>
				</div>
				<div class="flex justify-between">
					<div class="flex items-center gap-1">
						<h6>Unlock Date</h6>
						<inline-svg :src="infoIcon" class="rotate-180"></inline-svg>
					</div>
					<div class="flex items-center gap-1">
						{{ form.unlockDate === "3M" ? "3 Months" : form.unlockDate === "6M" ? "6 Months" : "1 Year" }}
					</div>
				</div>
				<div class="flex justify-between text-white">
					<div>Total</div>
					<div class="flex items-center gap-1">
						<div>
							{{
								numeral(Number(form.price) * Number(form.quantity)).format(
									"0[.]00[0000]",
									Math.floor
								)
							}}
						</div>
					</div>
				</div>
			</div>
			<div role="alert" class="alert bg-[#1E3034] text-sm mb-6 information-alert">
				<svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
						d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
				</svg>
				<span>You can create this offer without any financial investment</span>
			</div>
			<div class="flex justify-center">
				<button
					class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] h-[40px] hover:opacity-80 font-satoshiMedium font-normal disabled:opacity-20 disabled:bg-primary disabled:text-base-100">
					Indicate Interest
				</button>
			</div>
		</form>
	</Modal>
	<ModalConnect ref="connectModal"></ModalConnect>
</template>

<script setup>
import numeral from "numeral";
import inputIcon from "~/assets/images_new/icons/input.svg";
import usdtTokenIcon from "~/assets/images_new/tokens/usdt_3x.webp";
import api from "../utils/api";
import dayjs from "dayjs";
import { useAppKit, useAppKitAccount, useAppKitNetwork } from "@reown/appkit/vue";
import { useQuery } from "@tanstack/vue-query";
import { avalanche, mainnet, sepolia, solana, solanaDevnet, solanaTestnet, zksync } from "@reown/appkit/networks";
import { formatUSDT, multiplyNumberUsingDecimals } from "~/utils/number";
import { getAddress } from "ethers";

const props = defineProps({
	spotPrice: String,
	tokenId: String,
	tokenIconUrl: String,
	networkSymbol: String,
	tokenName: String,
	isLinkedTelegram: Boolean,
})

const MIN_SLIDER_VALUE = 50

const onSliderChange = (event) => {
	const value = Number(event.target.value)
	form.slider_value = value
	form.price = numeral(props.spotPrice * (50 + form.slider_value / 2) / 100).format("0[.][000000]", Math.floor)
	document.getElementById(
		"slider"
	).style.background = `linear-gradient(to right, #0C4CAC ${value}%, #575C74 ${value}%)`;
}

const onPriceChange = (event) => {
	const value = event.target.value
	form.price = value
	form.slider_value = Number(props.spotPrice) ? (Math.floor(Number(value) / Number(props.spotPrice) * 100) - 50) * 2 : 0
	document.getElementById(
		"slider"
	).style.background = `linear-gradient(to right, #0C4CAC ${form.slider_value}%, #575C74 ${form.slider_value}%)`;
}

const web3Store = useWeb3Store();
const newWeb3Store = useNewWeb3Store();
const { isLoggedIn, web3Kit } = storeToRefs(newWeb3Store);
const { networksBySymbol } = newWeb3Store
const networkData = useAppKitNetwork();
const account = useAppKitAccount();

const openModal = inject("openModal")

const form = reactive({
	price: null,
	slider_value: 0,
	unlockDate: "6M",
	quantity: null,
})

const openedCreateOfferModal = ref(false)
const errorMessage = ref(null)
const connectModal = ref(null);

const showLoginModal = () => {
	connectModal.value.handleClick();
	document.getElementById("connectModal").checked = true;
}

const handleOpenCreateOfferModal = () => {
	if (isLoggedIn.value) {
		if (props.networkSymbol === "SOL") {
			if (![solana.id, solanaDevnet.id, solanaTestnet.id].includes(networkData.value.chainId)) {
				useSweetAlertStore().showAlert("Error", `Please switch to ${props.networkSymbol} network to indicate interest for this token`, "error");
				return;
			}
		} else if (props.networkSymbol === "AVAX") {
			if (networkData.value.chainId !== avalanche.id) {
				useSweetAlertStore().showAlert("Error", `Please switch to ${props.networkSymbol} network to indicate interest for this token`, "error");
				return;
			}
		} else {
			if (![mainnet.id, sepolia.id].includes(networkData.value.chainId)) {
				useSweetAlertStore().showAlert("Error", `Please switch to ETH network to indicate interest for this token`, "error");
				return;
			}
		}

		form.price = props.spotPrice
		form.slider_value = 0
		form.quantity = null

		form.slider_value = 100
		document.getElementById(
			"slider"
		).style.background = `linear-gradient(to right, #0C4CAC ${form.slider_value}%, #575C74 ${form.slider_value}%)`;

		openedCreateOfferModal.value = true
		errorMessage.value = false
	} else {
		showLoginModal()
	}
}
const handleCloseCreateOfferModal = () => {
	openedCreateOfferModal.value = false
}

const linkToTelegram = async () => {
	openModal({
		type: "loading",
		title: "Linking your telegram account",
		message: "Proceed in telegram to complete linking step",
	})

	try {
		const timestamp = dayjs().valueOf()
		const networkSymbol = networksBySymbol[props.networkSymbol].nativeCurrency?.symbol
		const address = networkSymbol === "ETH" ? getAddress(account.value.address) : account.value.address

		const signMessageResponse = await web3Kit.value?.signMessage(
			`Connect with SecondSwap Telegram bot\n` +
			`Address: ${address}\n` +
			`Nonce: ${timestamp}`
		)

		const linkTelegramResponse = await api.apiCall("POST", "/telegram/link", {
			network: networkSymbol,
			nonce: Number(timestamp),
			signature: signMessageResponse.signature,
		})

		window.open(linkTelegramResponse.data.message.url, "_blank")

		let timeoutID;
		let intervalID = setInterval(async () => {
			if (!timeoutID) {
				timeoutID = setTimeout(() => {
					clearInterval(intervalID);
					openModal({
						type: "error",
						title: "Linking Unsucessful",
						message: "Oops we missed your connection, please try again",
						action: {
							name: "Try Again",
							onClick: linkToTelegram,
						}
					})
				}, 60000)
			}
			const res = await api.apiCall("GET", "/telegram/link/status");
			const status = res.data.message.status
			if (status !== "pending") {
				clearInterval(intervalID);

				if (status === "linked") {
					const userApiResponse = await api.apiCall("GET", "/user");
					const telegramHandle = userApiResponse.data.message.telegram;
					openModal({
						type: "success",
						title: "Telegram Linked",
						message: `Your telegram account ${telegramHandle ? `@${telegramHandle}` : ''} was successfully linked`,
					})
					clearTimeout(timeoutID)
					emit("onSuccess")
				} else {
					openModal({
						type: "error",
						title: "Linking Unsucessful",
						message: "Oops we missed your connection, please try again",
						action: {
							name: "Try Again",
							onClick: linkToTelegram,
						}
					})
				}
			}
		}, 2000);
	} catch (error) {
		if (error.status === 400) {
			openModal({
				type: "error",
				title: "Linking Unsucessful",
				message: "Telegram account is already linked",
			})
		} else {
			openModal({
				type: "error",
				title: "Linking Unsucessful",
				message: "Oops we missed your connection, please try again",
				action: {
					name: "Try Again",
					onClick: linkToTelegram,
				}
			})
		}
	}
}


const createOffer = async () => {
	// check token balance, currently disabled by adding `false &&`
	if (false && Number(web3Store.userInfo.nativeBalance) === 0) {
		errorMessage.value = "Only wallets with non-zero balance are able to create bids"
	} else {
		handleCloseCreateOfferModal()

		useSweetAlertStore().showLoadingAlert("Processing", "Proceed in your wallet")

		try {
			let unlockDate = dayjs();
			if (form.unlockDate === "3M") unlockDate = unlockDate.add(3, "month")
			else if (form.unlockDate === "6M") unlockDate = unlockDate.add(6, "month")
			else unlockDate = unlockDate.add(1, "year")
			unlockDate = unlockDate.valueOf()
			const res = await api.apiCall("POST", "/bid", {
				token_id: Number(props.tokenId),
				price: multiplyNumberUsingDecimals(form.price, USDT_DECIMALS).toString(),
				quantity: String(form.quantity),
				unlock_date: unlockDate,
			})

			if (props.isLinkedTelegram) {
				useSweetAlertStore().showAlert("Success", "Indicate Interest Success", "success");
			} else {
				useSweetAlertStore().showSuccessAlert({
					title: "Indicate Interest Success",
					content: `<div class="flex justify-center w-full text-[#84858E]">Get notified when ${props.tokenName} becomes available</div>`,
					confirmButton: `
						<div class="flex items-center justify-center h-full gap-2">
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.7766 2.47788L1.95657 7.03588C1.1499 7.35988 1.15457 7.80988 1.80857 8.01055L4.84323 8.95721L11.8646 4.52721C12.1966 4.32521 12.4999 4.43388 12.2506 4.65521L6.5619 9.78921H6.56057L6.5619 9.78988L6.35257 12.9179C6.65923 12.9179 6.79457 12.7772 6.96657 12.6112L8.44057 11.1779L11.5066 13.4425C12.0719 13.7539 12.4779 13.5939 12.6186 12.9192L14.6312 3.43388C14.8372 2.60788 14.3159 2.23388 13.7766 2.47788Z" fill="#181B2B"/></svg>
							<span class="text-base-100">Get notified</span>
						</div>
					`,
					onConfirm: linkToTelegram,
					footer: `<a href='/myassets'>View My Assets</a>`,
				});
			}
		} catch {
			useSweetAlertStore().showAlert("Error", "Failed to indicate interest", "error");
		}
	}
}


</script>

<style scoped>
.warning-alert {
	background-color: #2F2B34;
	color: #FFB784;
}

.information-alert {
	background-color: #1E3034;
	color: #50F187;
}

output {
	position: absolute;
	z-index: 10;
	bottom: 45px;
	left: 0;
}
</style>

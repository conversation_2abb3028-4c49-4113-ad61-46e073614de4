<template>
  <Teleport to="body">
    <input type="checkbox" id="listSucceedPrivateModal" class="modal-toggle" />
    <div
      :class="[
        isMobile
          ? isMounted
            ? 'translate-y-0 transform transition-all duration-500 ease-out'
            : 'translate-y-full transform transition-all duration-500 ease-out'
          : '',
        'md:translate-y-0',
      ]"
      role="dialog"
      class="modal bg-black/20 backdrop-blur-[12px] backdrop:bg-transparent"
    >
      <div
        class="modal-box font-medium w-full md:w-[530px] min-h-[650px] overflow-y-auto px-4 py-8 bg-modal rounded-[12px] fixed bottom-0 md:static transform transition-all duration-300 ease-out"
      >
        <div class="relative pt-16">
          <!-- Close button -->
          <div class="flex justify-end absolute top-0 right-0">
            <label
              for="listSucceedPrivateModal"
              @click="handleClose"
              class="btn btn-xs btn-circle btn-ghost text-lg text-white font-bold hover:bg-transparent focus:outline-none"
              >✕</label
            >
          </div>

          <!-- Success Icon and Title -->
          <div class="flex flex-col items-center justify-center mb-8">
            <div class="mb-6">
              <inline-svg
                class="w-[70px] h-[70px]"
                :src="checkCircleIcon"
              ></inline-svg>
            </div>
            <span class="text-xl font-medium text-white mb-3 leading-[22px]"
              >Listing Created!</span
            >
            <span
              class="text-white/50 text-center text-sm font-mediums leading-4"
            >
              Your lot for {{ formatToken(amount) }} {{ tokenTicker }} tokens
              has been created successfully.
            </span>
          </div>

          <!-- VIP Lot URL Section -->
          <div class="mb-5 bg-[#131627] p-5 rounded-lg">
            <h3 class="text-white/50 font-medium mb-3">VIP Lot URL</h3>
            <div
              class="flex items-center justify-between gap-2 mb-3 px-2 py-1.5 bg-[#FFFFFF0A] rounded-[4px]"
            >
              <div class="text-white truncate flex-1 min-w-0">
                {{ lotUrl }}
              </div>
              <button
                @click="copyUrl"
                class="btn btn-outline border-primary text-primary font-medium text-[14px] h-[26px] min-h-[26px] capitalize hover:bg-transparent hover:border-primary hover:text-primary !rounded-[4px] px-2"
              >
                <inline-svg
                  :src="copyIcon"
                  class="w-[14px] h-[14px]"
                ></inline-svg>
                Copy
              </button>
            </div>
            <p class="text-white/50 text-sm font-medium mb-3">
              Share this link with potential buyers to access your lot directly.
            </p>

            <!-- Share Icons -->
            <div class="flex gap-3 justify-center">
              <button
                @click="shareToX"
                class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500"
              >
                <inline-svg class="w-5" :src="xIcon"> </inline-svg>
              </button>
              <button
                @click="shareToTelegram"
                class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center hover:bg-gray-500"
              >
                <inline-svg class="w-4" :src="telegramIconWhite"> </inline-svg>
              </button>
            </div>
          </div>
          <!-- Reminder Section -->
          <div class="mb-5">
            <h3 class="text-white/50 text-[14px] font-medium mb-3">
              Reminder:
            </h3>
            <ul class="text-white/50 text-[14px]">
              <li class="flex items-start">
                <span
                  class="w-1 h-1 bg-white/50 rounded-full mt-[9px] mr-2 flex-shrink-0"
                ></span>
                User can navigate to the private lot and request access
              </li>
              <li class="flex items-start">
                <span
                  class="w-1 h-1 bg-white/50 rounded-full mt-[9px] mr-2 flex-shrink-0"
                ></span>
                After user issues access request - you are required to approve
                access
              </li>
              <li class="flex items-start">
                <span
                  class="w-1 h-1 bg-white/50 rounded-full mt-[9px] mr-2 flex-shrink-0"
                ></span>
                Upon approval user is able to purchase any amount of the lot
              </li>
            </ul>
          </div>

          <!-- Action Buttons -->
          <div class="flex gap-3">
            <button
              @click="viewLot"
              class="btn btn-outline border-primary text-white font-medium text-[14px] h-10 min-h-10 capitalize hover:bg-transparent hover:border-primary hover:text-primary flex-1"
            >
              View Lot
            </button>
            <button
              @click="confirm"
              class="btn btn-primary font-medium text-[14px] h-10 min-h-10 capitalize flex-1"
            >
              Confirm
            </button>
          </div>
        </div>
      </div>
      <label
        v-if="!isMobile"
        class="modal-backdrop"
        for="listSucceedPrivateModal"
        >Close</label
      >
    </div>
  </Teleport>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import { formatToken } from "~/utils/number";
import checkCircleIcon from "~/assets/images_new/icons/check-circle.svg";
import copyIcon from "~/assets/images_new/icons/copy.svg";
import xIcon from "~/assets/images_new/icons/social_media/x.svg";
import telegramIconWhite from "~/assets/images_new/icons/social_media/telegram-white.svg";

const props = defineProps({
  lotId: {
    type: String,
    required: true,
  },
  amount: {
    type: [String, Number],
    required: true,
  },
  tokenTicker: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["close", "viewLot"]);

// Modal state
const isMounted = ref(false);
const isMobile = ref(false);

// Generate lot URL
const lotUrl = computed(() => {
  const baseUrl = window.location.origin;
  return `${baseUrl}/${props.lotId}`;
});

// Modal functions
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = "hidden";
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

const handleClose = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = "auto";
  } else {
    isMounted.value = false;
  }
  emit("close");
};

const closeModal = () => {
  document.getElementById("listSucceedPrivateModal").checked = false;
  handleClose();
};

// Action functions
const copyUrl = async () => {
  try {
    await navigator.clipboard.writeText(lotUrl.value);
    useSweetAlertStore().showAlert(
      "Success",
      "URL copied to clipboard",
      "success"
    );
  } catch (err) {
    console.error("Failed to copy URL:", err);
    useSweetAlertStore().showAlert("Error", "Failed to copy URL", "error");
  }
};

const shareToX = () => {
  const text = `Check out my private token listing on SecondSwap!`;
  const url = `https://x.com/intent/tweet?text=${encodeURIComponent(
    text
  )}&url=${encodeURIComponent(lotUrl.value)}`;
  window.open(url, "_blank");
};

const shareToTelegram = () => {
  const url = encodeURIComponent(lotUrl.value);
  const text = encodeURIComponent(
    "Check out my private token listing on SecondSwap!"
  );
  const telegramUrl = `https://t.me/share/url?url=${url}&text=${text}`;

  window.open(telegramUrl, "_blank");
};

const viewLot = () => {
  emit("viewLot", props.lotId);
  closeModal();
};

const confirm = () => {
  closeModal();
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

defineExpose({
  handleClick,
});
</script>

<style scoped>
/* Custom styles if needed */
</style>

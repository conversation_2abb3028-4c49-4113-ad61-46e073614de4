<template>
  <Teleport to="body">
    <input type="checkbox" id="midnightSummaryModal" class="modal-toggle" />
    <div :class="[
      'md:translate-y-0',
    ]" role="dialog" class="modal bg-black/20 backdrop-blur-[12px] backdrop:bg-transparent font-medium">
      <div
        class="modal-box min-w-full md:min-w-[480px] md:max-w-[480px] pt-8 px-5 pb-6 bg-modal md:rounded-[12px] fixed bottom-0 md:static transform transition-all duration-500 ease-out">
        <div class="flex justify-end mb-8">
          <div class="modal-action">
            <label for="midnightSummaryModal"
              class="text-lg btn btn-xs btn-circle btn-ghost hover:bg-transparent focus:outline-none">✕</label>
          </div>
        </div>
        <div class="flex flex-col items-center">
          <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M70 37.2572V40.0172C69.9963 46.4864 67.9015 52.7812 64.028 57.9626C60.1545 63.1441 54.7098 66.9346 48.5061 68.7688C42.3023 70.6031 35.6718 70.3828 29.6034 68.1409C23.535 65.899 18.354 61.7555 14.8329 56.3284C11.3118 50.9013 9.63937 44.4813 10.065 38.0261C10.4907 31.5709 12.9916 25.4261 17.1948 20.5084C21.3981 15.5906 27.0784 12.1633 33.3886 10.7376C39.6988 9.3119 46.3008 9.96418 52.21 12.5972"
              stroke="#50F187" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" />
            <path d="M70 16L40 46L31 37.009" stroke="#50F187" stroke-width="3" stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>
          <div class="mt-8 text-xl font-medium text-center">
            <div>Claim <span class="text-success">{{ amount }} NIGHT</span></div>
            <div>Submitted Successfully!</div>
          </div>
          <div class="mt-2 text-sm text-center text-white/50">Your NIGHT token claim has been submitted and is now
            being processed. You
            can view
            your claim status in
            the My Assets section of your dashboard.</div>

          <div class="w-full p-6 mt-8 bg-modal-textbox rounded-xl">
            <div class="flex items-center justify-between">
              <div>
                <div>Claim Summary</div>
                <div class="text-sm text-white/50">Keep this information safe for your records.</div>
              </div>
              <div class="flex items-center gap-2">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_8263_5332)">
                    <path
                      d="M11.334 2.22676C12.3395 2.80733 13.1759 3.64044 13.7605 4.64362C14.3451 5.64681 14.6576 6.78527 14.6669 7.94632C14.6763 9.10736 14.3823 10.2507 13.814 11.2632C13.2457 12.2757 12.4228 13.1222 11.4268 13.719C10.4308 14.3157 9.29623 14.642 8.13538 14.6655C6.97454 14.689 5.82768 14.4089 4.80835 13.8529C3.78902 13.297 2.93256 12.4844 2.32376 11.4958C1.71496 10.5071 1.37492 9.37656 1.33732 8.21609L1.33398 8.00009L1.33732 7.78409C1.37465 6.63275 1.70968 5.51073 2.30974 4.52742C2.90981 3.54411 3.75442 2.73306 4.76125 2.17335C5.76807 1.61363 6.90275 1.32436 8.05465 1.33372C9.20656 1.34308 10.3364 1.65076 11.334 2.22676ZM10.472 6.19543C10.3572 6.08064 10.2045 6.01169 10.0424 6.0015C9.88042 5.99131 9.72025 6.04059 9.59198 6.14009L9.52932 6.19543L7.33398 8.39009L6.47198 7.52876L6.40932 7.47342C6.28104 7.37399 6.1209 7.32477 5.95892 7.33499C5.79694 7.34521 5.64426 7.41417 5.52949 7.52893C5.41473 7.6437 5.34577 7.79638 5.33555 7.95836C5.32533 8.12034 5.37455 8.28048 5.47398 8.40876L5.52932 8.47142L6.86265 9.80476L6.92532 9.86009C7.04223 9.9508 7.18601 10 7.33398 10C7.48196 10 7.62574 9.9508 7.74265 9.86009L7.80532 9.80476L10.472 7.13809L10.5273 7.07542C10.6268 6.94715 10.6761 6.78699 10.6659 6.62497C10.6557 6.46295 10.5868 6.31022 10.472 6.19543Z"
                      fill="#50F187" />
                  </g>
                  <defs>
                    <clipPath id="clip0_8263_5332">
                      <rect width="16" height="16" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
                <span class="text-sm text-success">Submitted</span>
              </div>
            </div>
            <div class="flex flex-col items-stretch gap-4 mt-8">
              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Wallet Eligibility</div>
                    <div class="mt-2 text-sm">{{ walletEligibility }}</div>
                  </div>
                  <button
                    class="flex items-center gap-2 px-0 h-[20px] text-sm border rounded-lg text-primary border-primary whitespace-nowrap flex-nowrap"><svg
                      width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.8132 5.63924C11.8132 5.49246 11.7844 5.34704 11.7283 5.21143C11.6721 5.07575 11.5894 4.95239 11.4856 4.84855C11.3818 4.74471 11.2584 4.66207 11.1227 4.60588C10.9871 4.54973 10.8417 4.521 10.6949 4.521H5.63973C5.34315 4.521 5.05875 4.63884 4.84904 4.84855C4.63933 5.05826 4.52149 5.34266 4.52148 5.63924V10.6944C4.52149 10.8412 4.55022 10.9866 4.60636 11.1222C4.66256 11.2579 4.7452 11.3813 4.84904 11.4851C4.95288 11.5889 5.07624 11.6716 5.21191 11.7278C5.34753 11.7839 5.49295 11.8127 5.63973 11.8127H10.6949C10.8417 11.8127 10.9871 11.7839 11.1227 11.7278C11.2584 11.6716 11.3818 11.5889 11.4856 11.4851C11.5894 11.3813 11.6721 11.2579 11.7283 11.1222C11.7844 10.9866 11.8132 10.8412 11.8132 10.6944V5.63924ZM12.6882 10.6944C12.6882 10.9561 12.6368 11.2154 12.5366 11.4572C12.4365 11.699 12.2893 11.9187 12.1042 12.1038C11.9192 12.2888 11.6995 12.436 11.4577 12.5361C11.2159 12.6363 10.9566 12.6877 10.6949 12.6877H5.63973C5.378 12.6877 5.11876 12.6363 4.87695 12.5361C4.63512 12.436 4.41548 12.2888 4.23039 12.1038C4.0453 11.9187 3.89818 11.699 3.79801 11.4572C3.69786 11.2154 3.64648 10.9561 3.64648 10.6944V5.63924C3.64649 5.1106 3.85658 4.60371 4.23039 4.2299C4.60419 3.85609 5.11109 3.646 5.63973 3.646H10.6949C10.9566 3.646 11.2159 3.69738 11.4577 3.79753C11.6995 3.8977 11.9192 4.04481 12.1042 4.2299C12.2893 4.41499 12.4365 4.63463 12.5366 4.87646C12.6368 5.11828 12.6882 5.37751 12.6882 5.63924V10.6944Z"
                        fill="#7CD3F8" />
                      <path
                        d="M1.3125 2.91667C1.3125 2.03338 2.03338 1.3125 2.91667 1.3125H8.75C9.05073 1.3125 9.31206 1.39191 9.53385 1.5529C9.69313 1.66852 9.81542 1.81455 9.91496 1.96704L10.0072 2.12085L10.0272 2.16073C10.1149 2.36445 10.0355 2.60546 9.83748 2.71558C9.63949 2.82564 9.39288 2.76596 9.26611 2.58398L9.24276 2.54582L9.18123 2.44385C9.12202 2.35341 9.06947 2.2969 9.02002 2.26099C8.96357 2.22001 8.88676 2.1875 8.75 2.1875H2.91667C2.51662 2.1875 2.1875 2.51662 2.1875 2.91667V8.74886L2.19434 8.84513C2.20718 8.94024 2.23856 9.03226 2.28719 9.11572C2.35207 9.22705 2.44527 9.31908 2.55721 9.38289C2.7671 9.50257 2.83979 9.77 2.72013 9.9799C2.60045 10.1897 2.33357 10.263 2.1237 10.1434C1.87791 10.0033 1.67368 9.80053 1.53125 9.55607C1.38883 9.31163 1.3132 9.03404 1.3125 8.75114V2.91667Z"
                        fill="#7CD3F8" />
                    </svg>
                    <span>Copy</span></button>
                </div>
              </div>

              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Network</div>
                    <div class="mt-2 text-sm capitalize">{{ network }}</div>
                  </div>
                </div>
              </div>

              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Destination Address (Cardano Address)</div>
                    <div class="mt-2 text-sm">{{ destinationAddress }}</div>
                  </div>
                  <button
                    class="flex items-center gap-2 px-0 h-[20px] text-sm border rounded-lg text-primary border-primary whitespace-nowrap flex-nowrap"><svg
                      width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M11.8132 5.63924C11.8132 5.49246 11.7844 5.34704 11.7283 5.21143C11.6721 5.07575 11.5894 4.95239 11.4856 4.84855C11.3818 4.74471 11.2584 4.66207 11.1227 4.60588C10.9871 4.54973 10.8417 4.521 10.6949 4.521H5.63973C5.34315 4.521 5.05875 4.63884 4.84904 4.84855C4.63933 5.05826 4.52149 5.34266 4.52148 5.63924V10.6944C4.52149 10.8412 4.55022 10.9866 4.60636 11.1222C4.66256 11.2579 4.7452 11.3813 4.84904 11.4851C4.95288 11.5889 5.07624 11.6716 5.21191 11.7278C5.34753 11.7839 5.49295 11.8127 5.63973 11.8127H10.6949C10.8417 11.8127 10.9871 11.7839 11.1227 11.7278C11.2584 11.6716 11.3818 11.5889 11.4856 11.4851C11.5894 11.3813 11.6721 11.2579 11.7283 11.1222C11.7844 10.9866 11.8132 10.8412 11.8132 10.6944V5.63924ZM12.6882 10.6944C12.6882 10.9561 12.6368 11.2154 12.5366 11.4572C12.4365 11.699 12.2893 11.9187 12.1042 12.1038C11.9192 12.2888 11.6995 12.436 11.4577 12.5361C11.2159 12.6363 10.9566 12.6877 10.6949 12.6877H5.63973C5.378 12.6877 5.11876 12.6363 4.87695 12.5361C4.63512 12.436 4.41548 12.2888 4.23039 12.1038C4.0453 11.9187 3.89818 11.699 3.79801 11.4572C3.69786 11.2154 3.64648 10.9561 3.64648 10.6944V5.63924C3.64649 5.1106 3.85658 4.60371 4.23039 4.2299C4.60419 3.85609 5.11109 3.646 5.63973 3.646H10.6949C10.9566 3.646 11.2159 3.69738 11.4577 3.79753C11.6995 3.8977 11.9192 4.04481 12.1042 4.2299C12.2893 4.41499 12.4365 4.63463 12.5366 4.87646C12.6368 5.11828 12.6882 5.37751 12.6882 5.63924V10.6944Z"
                        fill="#7CD3F8" />
                      <path
                        d="M1.3125 2.91667C1.3125 2.03338 2.03338 1.3125 2.91667 1.3125H8.75C9.05073 1.3125 9.31206 1.39191 9.53385 1.5529C9.69313 1.66852 9.81542 1.81455 9.91496 1.96704L10.0072 2.12085L10.0272 2.16073C10.1149 2.36445 10.0355 2.60546 9.83748 2.71558C9.63949 2.82564 9.39288 2.76596 9.26611 2.58398L9.24276 2.54582L9.18123 2.44385C9.12202 2.35341 9.06947 2.2969 9.02002 2.26099C8.96357 2.22001 8.88676 2.1875 8.75 2.1875H2.91667C2.51662 2.1875 2.1875 2.51662 2.1875 2.91667V8.74886L2.19434 8.84513C2.20718 8.94024 2.23856 9.03226 2.28719 9.11572C2.35207 9.22705 2.44527 9.31908 2.55721 9.38289C2.7671 9.50257 2.83979 9.77 2.72013 9.9799C2.60045 10.1897 2.33357 10.263 2.1237 10.1434C1.87791 10.0033 1.67368 9.80053 1.53125 9.55607C1.38883 9.31163 1.3132 9.03404 1.3125 8.75114V2.91667Z"
                        fill="#7CD3F8" />
                    </svg>
                    <span>Copy</span></button>
                </div>
              </div>

              <div class="p-4 rounded bg-modal">
                <div class="flex items-center justify-between">
                  <div>
                    <div class="text-xs text-white/50">Submission Time</div>
                    <div class="mt-2 text-sm">{{ submissionTime }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <nuxt-link to="/myassets" class="w-full">
            <div class="w-full h-10 mt-6 btn btn-primary btn-sm">
              Go to My Assets
            </div>
          </nuxt-link>
        </div>
      </div>
    </div>
    <label class="modal-backdrop" for="midnightSummaryModal">Close</label>
  </Teleport>
</template>

<script setup>
const props = defineProps({
  amount: Number,
  walletEligibility: String,
  network: String,
  destinationAddress: String,
  submissionTime: String,
})
</script>

<style scoped></style>

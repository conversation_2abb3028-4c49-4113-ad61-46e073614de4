<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition duration-200 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition duration-150 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isVisible"
        id="searchModal"
        class="fixed inset-0 z-50 w-full h-full"
      >
        <div
          class="absolute inset-0 z-[100] bg-[#090C1E] pt-[75px] flex flex-col overflow-y-scroll animate-fadeIn"
        >
          <div
            class="flex flex-row items-center justify-between w-full gap-4 px-6"
          >
            <input
              type="text"
              :placeholder="'Search for lot number'"
              class="h-10 min-h-10 bg-transparent border border-[#343849] rounded-full focus:ring-0 focus:outline-none pl-4 text-white w-full"
              v-model="searchTerm"
              autofocus
            />
            <inline-svg
              :src="closeIcon"
              @click="$emit('closeModal')"
              class="w-[24px] h-[24px] cursor-pointer"
            ></inline-svg>
          </div>

          <!-- <button @click="getAllLots('BEST_PRICE')" class="px-4 py-2 text-black bg-white rounded-full">Get All Lots</button> -->
          <!-- Debug info -->
          <!-- <div class="px-6 mt-4 text-white/50">
          <p>Search term: {{ searchTerm }}</p>
          <p>Results count: {{ props.lots?.length || 0 }}</p>
        </div>

        <div class="px-6 mt-4 text-white/50">{{ props.lots.network_image }}</div> -->

          <!-- Mobile view -->
          <div class="px-6 pb-4 mt-6 overflow-y-scroll lg:hidden">
            <div v-if="isSearching" class="mt-8 text-center text-white">
              Searching...
            </div>
            <div
              v-else-if="!isSearching && (!lots || lots.length === 0)"
              class="mt-8 text-center text-white"
            >
              <CardAllLot
                @showBuyModalEvent="
                  isLoggedIn
                    ? $emit('showBuyModal', $event.lot || lot)
                    : $emit('showLoginModal')
                "
                :index="index"
                :tokenImg="lot.token_image"
                :networkImg="lot.network_image"
                :networkSymbol="lot.network_symbol"
                :totalListing="lot.total_listing"
                :remainingListing="lot.remaining_listing"
                :tokenName="lot.token_name"
                :tokenTicker="lot.token_ticker"
                :tokenDecimal="lot.token_decimal"
                :listId="lot.list_id"
                :tokenPrice="
                  divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS).toString()
                "
                :bestPrice="
                  divideNumberUsingDecimals(
                    lot.listed_price,
                    USDT_DECIMALS
                  ).toString()
                "
                :maxDiscount="lot.max_discount"
                :unlockStart="lot.start_date"
                :displayId="lot.display_id"
                :twfHourChanges="`${
                  twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
                }${lot.twf_hour_changes.replace('-', '')}%`"
                :twfHourColor="
                  twfChangesInfo[lot.twf_hour_changes * 1 > 0].color
                "
                :lotId="
                  common.formatLotId(
                    lot.token_ticker,
                    lot.display_id,
                    lot.list_id
                  )
                "
                :buttonText="'Buy'"
              ></CardAllLot>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import inlineSvg from "vue-inline-svg";
import searchIcon from "~/assets/images_new/icons/search.svg";
import closeIcon from "~/assets/images_new/icons/close.svg";
import { divideNumberUsingDecimals } from "~/utils/number";
import { USDT_DECIMALS } from "~/utils/const";

const newWeb3Store = useNewWeb3Store();
const { isLoggedIn } = storeToRefs(newWeb3Store);
const route = useRoute();

const props = defineProps({
  twfChangesInfo: {
    type: Object,
    required: true,
  },
  lots: {
    type: Array,
    required: true,
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
  tokenAddress: Object,
  sort_by: Object,
});

const lots = ref(props.lots || []);

watch(props.lots, (newValue) => {
  console.log("props.lots newValue", newValue);
  lots.value = newValue;
});

const emit = defineEmits([
  "closeModal",
  "showBuyModal",
  "showLoginModal",
  "update:modelValue",
]);

const page = ref(1);

const searchTerm = ref(null);

const isSearching = ref(false);

let debounceTimer;

watch(
  () => searchTerm.value,
  (newValue) => {
    console.log("searchTerm newValue", newValue);
    isSearching.value = true;
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(async () => {
      await getTokenLots(props.sort_by, newValue);
      isSearching.value = false;
    }, 500);
  }
);

const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

async function getTokenLots(sort_by, display_id) {
  try {
    const res = await api.apiCall("GET", "/marketplace/single-token-lot-info", {
      network: route.query.network,
      token_address: props.tokenAddress,
      sort_by: sort_by,
      display_id: display_id,
    });

    console.log("token lots res", res.data.message);

    lots.value = res.data.message.data;
  } catch (error) {
    console.error("token lots error", error);
  }
}

defineExpose({
  searchTerm: searchTerm,
});
</script>

<style scoped>
.overflow-y-auto {
  max-height: calc(100vh - 140px);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}
</style>

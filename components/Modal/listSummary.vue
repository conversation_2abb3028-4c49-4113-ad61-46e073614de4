<template>
  <Teleport to="body">
    <input type="checkbox" id="newListSummaryModal" class="modal-toggle" />
    <div
      :class="[
        isMobile
          ? isMounted
            ? 'translate-y-0 transform transition-all duration-500 ease-out'
            : 'translate-y-full transform transition-all duration-500 ease-out'
          : '',
        'md:translate-y-0',
      ]"
      role="dialog"
      class="modal bg-black/20 backdrop-blur-[12px] backdrop:bg-transparent"
    >
      <div
        v-if="selectedPlan && listForm"
        class="modal-box font-medium min-w-full md:min-w-[460px] md:max-w-[460px] min-h-[650px] overflow-y-auto md:min-h-[700px] md:max-h-[700px] px-5 pt-8 pb-6 bg-modal rounded-[12px] fixed bottom-0 md:static transform transition-all duration-300 ease-out"
      >
        <div class="flex items-center justify-between mb-8 md:mb-10">
          <div
            class="flex items-center gap-1 text-white cursor-pointer"
            @click="backToListModal"
          >
            <inline-svg class="w-[14px] h-[14px]" :src="backIcon"></inline-svg>
            <span class="text-sm">Back</span>
          </div>
          <h3 class="text-xl leading-5 text-white">Summary</h3>
          <div for="modal-action">
            <label
              for="newListSummaryModal"
              @click="handleClose"
              class="text-lg text-white btn btn-xs btn-circle btn-ghost hover:bg-transparent focus:outline-none"
              >✕</label
            >
          </div>
        </div>
        <!-- <div>list form {{ listForm }}</div> -->
        <!-- <div>selected plan {{ props.selectedPlan }}</div> -->
        <form @submit.prevent="listAction" class="font-medium">
          <div class="flex flex-col items-center justify-center mb-8">
            <img
              :src="selectedPlan.token_image"
              alt="token-img"
              class="w-[64px] h-[64px] rounded-full mb-3 coin-border"
            />
            <h5 class="text-sm font-normal leading-4 opacity-60">
              Token Amount
            </h5>
            <h3 class="text-[28px] font-satoshiBlack leading-10 text-white">
              {{ formatToken(listForm.amount) }}
            </h3>
            <h5 class="text-xs leading-3 text-white opacity-60">
              {{ selectedPlan.token_ticker }} Price
              {{
                formatUSDT(
                  divideNumberUsingDecimals(
                    selectedPlan.token_price,
                    USDT_DECIMALS
                  )
                )
              }}

              USDT
            </h5>
          </div>
          <div
            class="bg-modal-textbox max-h-[310px] overflow-auto rounded-[8px] text-sm text-input-icons p-5 font-medium mb-4 md:mb-6 leading-4"
          >
            <div class="flex justify-between mb-3 md:mb-4">
              <div>Price</div>
              <div class="flex items-center gap-1">
                <h6>
                  {{ formatUSDT(listForm.pricePerToken) }}
                </h6>
                <img
                  class="w-[14px] h-[14px] coin-border rounded-full"
                  :src="usdtToken"
                  alt="token-img"
                />
              </div>
            </div>
            <div class="flex justify-between mb-3 md:mb-4">
              <div>Fill type</div>
              <h6 class="capitalize">
                {{ fillTypeText[listForm.fillType] }}
              </h6>
            </div>
            <div class="flex justify-between mb-3 md:mb-4">
              <div>Pool type</div>
              <h6
                class="capitalize flex items-center gap-1"
                :class="{
                  'text-[#FCC951] bg-[#FCC9511A] px-[6px] py-[1.5px] rounded-3xl':
                    listForm.listType === '1',
                }"
              >
                <inline-svg
                  v-if="listForm.listType === '1'"
                  :src="yellowCrownIcon"
                  width="12"
                  height="12"
                ></inline-svg>

                {{ listTypeText[listForm.listType] }}
              </h6>
            </div>
            <div class="flex justify-between">
              <div class="text-white text-2sm">Subtotal</div>
              <div class="flex items-center gap-1">
                <h6 class="text-white text-2sm">
                  {{
                    formatUSDT(
                      toBigNumber(listForm.pricePerToken).multipliedBy(
                        listForm.amount
                      )
                    )
                  }}
                </h6>
                <img
                  class="w-[14px] h-[14px] coin-border rounded-full"
                  :src="usdtToken"
                  alt="token-img"
                />
              </div>
            </div>
            <hr class="border-0.5 border-textbox-border my-3 md:my-5" />
            <div class="flex flex-col gap-3 mb-4 md:gap-4">
              <div class="flex items-center justify-between">
                <div class="text-white">Details</div>
                <button
                  type="button"
                  @click="toggleDetails"
                  class="flex items-center gap-1 text-sm text-white/60 hover:text-white/80"
                >
                  <inline-svg
                    :class="showDetails ? 'rotate-180' : ''"
                    class="w-4 h-4 transition-transform"
                    :src="arrowDownIcon"
                  ></inline-svg>
                </button>
              </div>

              <Transition
                enter-active-class="transition duration-300 ease-out"
                enter-from-class="transform -translate-y-4 opacity-0"
                enter-to-class="transform translate-y-0 opacity-100"
                leave-active-class="transition duration-200 ease-in"
                leave-from-class="transform translate-y-0 opacity-100"
                leave-to-class="transform -translate-y-4 opacity-0"
              >
                <div v-if="showDetails">
                  <div
                    class="flex justify-between mb-4 text-sm font-medium text-white/60"
                  >
                    <h6>Discount Type</h6>
                    <div class="flex items-center gap-1">
                      <div class="capitalize">
                        {{ discountTypeText[listForm.discountType] }}
                      </div>
                      <div class="relative inline-block pl-2 group">
                        <inline-svg
                          :src="infoIcon"
                          class="w-3 h-3 stroke-input-icons"
                        ></inline-svg>
                        <div
                          class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[140px] max-h-[100px] right-full top-1/2 -translate-y-1/2 shadow-lg"
                        >
                          <div
                            class="my-2 px-3 max-h-[80px] w-[130px] overflow-y-scroll"
                          >
                            <div class="flex flex-col gap-3">
                              <h5>
                                <b>Linear Discount: </b>
                                Up to 5% discount based on the proportion of
                                total supply purchased. The more you buy, the
                                larger the discount.
                              </h5>
                              <h5>
                                <b>Fixed Discount: </b>
                                A fixed percentage discount (e.g., 5%) applied
                                only to a single fill lot, requiring the entire
                                lot to be purchased.
                              </h5>
                            </div>
                          </div>

                          <div
                            class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-t border-r -right-1 top-1/2 bg-modal-textbox border-textbox-border"
                          ></div>
                            
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="flex justify-between mb-4 text-sm font-medium text-white/60"
                  >
                    <h6>Total Token Amount</h6>
                    <div class="flex items-center gap-1">
                      <div class="capitalize">
                        {{ formatUSDT(listForm.amount) }}
                      </div>
                      <img
                        :src="selectedPlan.token_image"
                        alt="token-img"
                        class="w-[14px] h-[14px] coin-border rounded-full"
                      />
                    </div>
                  </div>
                  <div
                    class="flex justify-between text-sm font-medium text-white/60"
                  >
                    <div class="flex items-center gap-1 text-input-icons">
                      <h6>Maximum Discount</h6>
                    </div>
                    <div class="flex items-center gap-1">
                      <div>
                        {{
                          listForm.linearDiscountPct != 0
                            ? listForm.linearDiscountPct
                            : listForm.fixDiscountPct
                        }}%
                      </div>
                    </div>
                  </div>
                </div>
              </Transition>

              <div class="flex justify-between text-sm text-success">
                <div>
                  Discount (-{{
                    listForm.linearDiscountPct != 0
                      ? listForm.linearDiscountPct
                      : listForm.fixDiscountPct
                  }}%)
                </div>
                <div class="flex items-center gap-1">
                  <div>
                    {{
                      formatUSDT(
                        listForm.linearDiscountPct != 0
                          ? toBigNumber(listForm.amount)
                              .multipliedBy(listForm.pricePerToken)
                              .multipliedBy(listForm.linearDiscountPct)
                              .dividedBy(100)
                          : toBigNumber(listForm.amount)
                              .multipliedBy(listForm.pricePerToken)
                              .multipliedBy(listForm.fixDiscountPct)
                              .dividedBy(100)
                      )
                    }}
                  </div>
                  <img
                    class="w-[14px] h-[14px] coin-border rounded-full"
                    :src="usdtToken"
                    alt="token-img"
                  />
                </div>
              </div>

              <div
                class="flex justify-between text-sm font-medium text-white/60"
              >
                <div class="flex items-center text-input-icons">
                  <h6>Platform Fees</h6>
                  <div class="relative inline-block pl-1 pr-2 group">
                    <div class="">
                      <inline-svg
                        :src="infoIcon"
                        class="w-3 h-3 stroke-input-icons"
                      ></inline-svg>
                    </div>
                    <div
                      class="absolute invisible group-hover:visible opacity-0 group-hover:opacity-100 transition bg-modal-textbox border border-textbox-border text-white text-sm rounded-lg w-[130px] max-h-[100px] left-full top-1/2 -translate-y-1/2 shadow-lg"
                    >
                      <div
                        class="my-2 px-3 max-h-[80px] w-full overflow-y-scroll"
                      >
                        A platform fee of {{ selectedPlan.seller_fee }}% is
                        charged upon completion of purchase notional value and
                        charged in the quote currence
                      </div>

                      <!-- Triangle pointer -->
                      <div
                        class="absolute w-2 h-2 rotate-45 -translate-y-1/2 border-b border-l -left-1 top-1/2 bg-modal-textbox border-textbox-border"
                      ></div>
                        
                    </div>
                  </div>
                </div>
                <div class="flex items-center gap-1">
                  <div>
                    {{ formatUSDT(platformFee) }}
                  </div>
                  <img
                    class="w-[14px] h-[14px]"
                    :src="usdtToken"
                    alt="token-img"
                  />
                </div>
              </div>
            </div>
            <div class="flex items-center justify-between text-white text-2sm">
              <div class="flex items-center gap-1">
                <h6>Amount to Receive</h6>
              </div>
              <div class="flex items-center gap-1">
                <h6>
                  {{ formatUSDT(amountToReceive) }}
                </h6>
                <img
                  class="w-[14px] h-[14px]"
                  :src="usdtToken"
                  alt="token-img"
                />
              </div>
            </div>
          </div>

          <div class="left-0 right-0 md:absolute bottom-6 lg:px-5 bg-modal">
            <div class="flex justify-center mb-4">
              <button
                class="btn btn-primary text-base-100 text-2sm w-full min-h-[40px] max-h-[40px] hover:opacity-80 font-medium"
              >
                List Now
              </button>
            </div>
            <div class="flex items-center justify-center gap-1 font-medium">
              <inline-svg :src="gasIcon"></inline-svg>
              <h5 class="text-sm leading-3 text-input-icons">
                Est. Network Fee:
                <span v-if="isCalculatingNetworkFee">Calculating...</span>
                <span v-else-if="networkFee && networkFee > 0"
                  >{{
                    numeral(
                      divideNumberUsingDecimals(networkFee, 18).toString()
                    ).format("0.[00000000]", Math.round())
                  }}
                  {{ currentNetwork }}</span
                >
                <span v-else-if="networkFee == -1n">Failed to load.</span>
              </h5>
            </div>
          </div>
        </form>
      </div>
      <label v-if="!isMobile" class="modal-backdrop" for="newListSummaryModal"
        >Close</label
      >
    </div>

    <!-- ListSucceedPrivate Modal -->
    <ListSucceedPrivate
      v-if="showPrivateSuccessModal"
      :lot-id="createdLotId"
      :amount="listForm.amount"
      :token-ticker="selectedPlan.token_ticker"
      @close="closePrivateSuccessModal"
      @viewLot="handleViewLot"
      @confirm="handleConfirm"
      ref="listSucceedPrivateModal"
    />
  </Teleport>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import numeral from "numeral";
import walletIcon from "~/assets/images_new/icons/buy-wallet.svg";
import usdtToken from "~/assets/images_new/tokens/usdt_3x.webp";
import inputIcon from "~/assets/images_new/icons/input.svg";
import infoIcon from "~/assets/images_new/icons/info.svg";
import gasIcon from "~/assets/images_new/icons/gas.svg";
import arrowDownIcon from "~/assets/images_new/icons/chevron-down.svg";
import backIcon from "~/assets/images_new/icons/chevron-left.svg";
import yellowCrownIcon from "~/assets/images_new/icons/yellow-crown.svg";
import { USDT_DECIMALS } from "~/utils/const";
import {
  toBigNumber,
  formatUSDT,
  divideNumberUsingDecimals,
  formatToken,
} from "~/utils/number";
import ListSucceedPrivate from "./ListSucceedPrivate.vue";

const props = defineProps({
  selectedPlan: Object,
  listForm: Object,
  currentNetwork: String,
});

console.log("list summary currentNetwork", props.currentNetwork);

const emit = defineEmits(["callback", "openListModal"]);
const newApproveModal = ref(null);

// Private success modal state
const showPrivateSuccessModal = ref(false);
const createdLotId = ref(null);
const listSucceedPrivateModal = ref(null);

// const { listForm } = toRefs(props);

console.log("props", props);

const web3Store = useWeb3Store();

const route = useRoute();
const router = useRouter();
let isApprove = ref(null);
let approveStatus = ref(null);
const summaryListForm = ref();

// watch(
//   props,
//   (newProps, oldProps) => {
//     console.log("props from list summary", newProps, oldProps);
//   },
//   { deep: true }
// );

const fillTypeText = {
  0: "partial",
  1: "single",
};

const discountTypeText = {
  0: "no discount",
  1: "linear",
  2: "fixed",
};

const listTypeText = {
  0: "public",
  1: "VIP",
};

async function listAction() {
  console.log("selectedPlan", props.selectedPlan);
  document.getElementById("newListSummaryModal").checked = false;
  useSweetAlertStore().showLoadingAlert("Processing", "Listing");

  console.log(
    "vesting address after listing",
    props.selectedPlan.vesting_address
  );
  console.log("list form after listing", props.listForm);

  try {
    const result = await useWeb3Store().listLot(
      props.selectedPlan.vesting_address,
      props.listForm,
      props.selectedPlan.token_decimal,
      props.currentNetwork
    );

    console.log("Listing result:", result);

    // Start enhanced polling for status updates if we have a marketplace ID
    if (result && result.listingId) {
      if (props.listForm.listType === "1") {
        const res = await handleGetPendingLotId(result.listingId);

        createdLotId.value = res?.lotId || "";
        showPrivateSuccessModal.value = true;

        setTimeout(() => {
          useSweetAlertStore().close();

          if (listSucceedPrivateModal.value) {
            listSucceedPrivateModal.value.handleClick();
          }

          const modalCheckbox = document.getElementById(
            "listSucceedPrivateModal"
          );
          if (modalCheckbox) {
            modalCheckbox.checked = true;
          }
        }, 300);
      } else {
        useSweetAlertStore().showAlert("Success", "List success", "success");

        setTimeout(() => {
          emit("callback");
        }, 3000);
      }
    }
  } catch (error) {
    console.error(error);
    useSweetAlertStore().showAlert("error", "Transaction Declined", "error");
  }
}

const handleGetPendingLotId = async (listingId) => {
  console.log("handleGetPendingLotId", props.selectedPlan.vesting_address);
  try {
    const apiRes = await api.apiCall(
      "POST",
      `/private-lot/create`,
      {
        vestingPlanAddress: props.selectedPlan.vesting_address,
        listId: listingId,
      }
    );

    if (apiRes.data) {
      const vestingId = apiRes.data?.message?.vestingPlan?.vesting_id;
      const displayId = apiRes.data?.message?.vestingPlan?.display_id;
      const lotId = `${vestingId}-${displayId}-${listingId}`;
      return {
        lotId,
      };
    }
  } catch (apiError) {
    console.error("Failed to create pending listing id:", apiError);
  }
};

// modal function
const isMounted = ref(false);
const isMobile = ref(false);
const networkFee = ref(null);
const isCalculatingNetworkFee = ref(false);

watch(isMounted, async () => {
  if (isMounted) {
    try {
      isCalculatingNetworkFee.value = true;
      networkFee.value = await web3Store.getListLotNetworkFee(
        props.selectedPlan.vesting_address,
        props.listForm,
        props.selectedPlan.token_decimal
      );
    } catch (err) {
      console.error("err", err);
    } finally {
      isCalculatingNetworkFee.value = false;
    }
  }
});

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = "hidden";
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = "auto";
  } else {
    isMounted.value = false;
  }
};

const handleClose = () => {
  closeModal();
};

const backToListModal = () => {
  emit("openListModal");
  closeModal();
  document.getElementById("newListSummaryModal").checked = false;
  document.getElementById("newListModal").checked = true;
};

onMounted(() => {
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

defineExpose({
  handleClick,
});

const showDetails = ref(false);

const toggleDetails = () => {
  showDetails.value = !showDetails.value;
};

const discountAmount = computed(() => {
  const totalAmount = props.listForm.amount * props.listForm.pricePerToken;
  return props.listForm.linearDiscountPct != 0
    ? (totalAmount * props.listForm.linearDiscountPct) / 100
    : (totalAmount * props.listForm.fixDiscountPct) / 100;
});

const discountedTotal = computed(() => {
  const totalAmount = props.listForm.amount * props.listForm.pricePerToken;
  return totalAmount - discountAmount.value;
});

const platformFee = computed(() => {
  return (discountedTotal.value * props.selectedPlan.seller_fee) / 100;
});

const amountToReceive = computed(() => {
  return discountedTotal.value - platformFee.value;
});

// Private success modal handlers
const closePrivateSuccessModal = () => {
  showPrivateSuccessModal.value = false;
  emit("callback");
};

const handleViewLot = (lotId) => {
  // Navigate to the lot page
  router.push(`/${lotId}?network=${props.currentNetwork}`);
};

const handleConfirm = () => {
  // Handle confirm action - could show a confirmation or redirect
  useSweetAlertStore().showAlert(
    "Success",
    "Private listing confirmed successfully",
    "success"
  );
};
</script>

<style></style>

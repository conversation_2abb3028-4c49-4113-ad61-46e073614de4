<template>
  <Teleport to="body">
    <input type="checkbox" id="connectModal" class="modal-toggle" />

    <div class="modal" role="dialog">
      <div
        :class="[
          isMobile
            ? isMounted
              ? 'translate-y-0 transform transition-all duration-500 ease-out'
              : 'translate-y-full transform transition-all duration-500 ease-out'
            : '',
          'md:translate-y-0',
        ]"
        class="modal-box font-medium px-5 py-8 overflow-hidden md:px-6 md:absolute md:top-[72px] md:right-14 md:overflow-hidden min-w-full md:min-w-[326px] md:max-w-[326px] md:h-[228px] bg-modal fixed bottom-0 w-full transform transition-all duration-500 ease-out border border-[#343849] !shadow-[0px_0px_14px_rgba(255,255,255,0.08)] rounded-xl"
      >
        <label
          for="connectModal"
          @click="handleClose"
          class="absolute p-0 text-lg btn btn-sm btn-circle btn-ghost right-3 top-5 lg:right-3 lg:top-4"
        >
          ✕
        </label>
        <div class="flex items-center justify-center h-full">
          <div class="flex flex-col items-center">
            <img :src="logoBlue" class="mb-4 w-[32px] h-[32px]" />
            <div class="mb-3 text-white capitalize text-2sm">
              Connect & Verify
            </div>
            <div class="mb-2 text-sm font-medium text-input-icons">
              <div class="mb-2 text-center">
                Sign for free to connect your wallet, agreeing to
                <a
                  class="text-primary"
                  href="https://docs.secondswap.io/misc/legal-disclaimer/terms-of-use"
                  target="_blank"
                  >SecondSwap's Terms</a
                >
                and confirming you're not a Restricted Person
              </div>
            </div>
            <div
              v-if="account.isConnected"
              class="flex justify-between w-full gap-2"
            >
              <button
                class="btn btn-outline border-primary text-primary h-10 min-h-10 capitalize w-[49%] hover:bg-transparent hover:border-primary hover:text-primary"
                @click="disconnect"
              >
                disconnect
              </button>
              <button
                class="btn btn-primary h-10 min-h-10 capitalize w-[49%]"
                @click="signLogin"
              >
                sign
              </button>
            </div>
            <button
              v-else
              class="w-full h-10 font-medium capitalize btn btn-primary min-h-10"
              @click="connectWallet()"
            >
              <span
                v-if="isConnecting"
                class="loading loading-spinner loading-md"
              />
              <span v-else>connect</span>
            </button>
          </div>
        </div>
      </div>
      <label v-if="!isMobile" class="modal-backdrop" for="connectModal"
        >Close</label
      >
    </div>
  </Teleport>
</template>

<script setup>
import { solana, solanaDevnet, solanaTestnet } from "@reown/appkit/networks";
import { useAppKitAccount, useAppKitNetwork } from "@reown/appkit/vue";
import dayjs from "dayjs";
import { getAddress } from "ethers";
import logoBlue from "~/assets/images/common/logo_blue_1_5x.webp";

const web3Store = useWeb3Store();
const newWeb3Store = useNewWeb3Store();
const { web3Kit } = storeToRefs(newWeb3Store);
const account = useAppKitAccount();
const networkData = useAppKitNetwork();

async function signLogin() {
  const timestamp = dayjs().format("x");
  const res = await web3Kit.value?.signMessage(
    `Welcome to SecondSwap ${timestamp}`,
    timestamp
  );

  const isSolanaNetwork = !![solana.id, solanaDevnet.id, solanaTestnet.id].find(
    (item) => item === networkData.value.chainId
  );
  const apiRes = await api.apiCall("POST", "/user/wallet-login", {
    ...(isSolanaNetwork
      ? {
          network: "SOL",
          address: account.value.address,
        }
      : {
          address: getAddress(account.value.address),
        }),
    signature: res.signature,
    nonce: timestamp,
    is_prompt: true,
  });

  console.log(apiRes);

  localStorage.setItem("isLogin", true);
  web3Store.$patch({
    isLogin: true,
  });
  newWeb3Store.setIsLoggedIn(true);
  closeModal();
  document.getElementById("connectModal").checked = false;

  //   const router = useRouter();
  //   router.push({ path: `/myassets` });
}

function disconnect() {
  newWeb3Store.disconnect();
  closeModal();
  document.getElementById("connectModal").checked = false;
}

definePageMeta({
  layout: "new-app",
});

// modal function
const isMounted = ref(false);
const isMobile = ref(false);
const isConnecting = ref(false);

onUnmounted(() => {
  window.removeEventListener("resize", checkMobile);
});

const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

const handleClick = () => {
  checkMobile();
  if (isMobile.value) {
    document.body.style.overflow = "hidden";
  }
  setTimeout(() => {
    isMounted.value = true;
  }, 50);
};

onMounted(() => {
  // if (web3Store.isInit) {
  //   web3Store
  //     .getTokenBalance(useRuntimeConfig().public.usdtAddress)
  //     .then((bal) => {
  //       usdtBalance.value = bal;
  //     });
  // }
  checkMobile();
  window.addEventListener("resize", checkMobile);
});

const closeModal = () => {
  if (isMobile.value) {
    isMounted.value = false;
    document.body.style.overflow = "auto";
  } else {
    isMounted.value = false;
    document.body.style.overflow = "auto";
    document.body.style.position = "";
  }
  console.log("close modal executed isMobile", isMobile.value);
};

const handleClose = () => {
  closeModal();
};

const connectWallet = async () => {
  isConnecting.value = true;
  try {
    await newWeb3Store.connectWallet();
    isConnecting.value = false;
  } catch (error) {
    console.error(error);
  } finally {
    setTimeout(() => {
      isConnecting.value = false;
    }, 3000);
  }
};

defineExpose({
  handleClick,
});
// useRouteStore().$patch({
//   previousUrl: null,
//   name: "Connect Wallet",
// });
</script>

<style lang="scss" scoped></style>

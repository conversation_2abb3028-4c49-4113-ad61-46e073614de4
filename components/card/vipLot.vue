<template>
  <div
    class="card-lot w-full md:max-w-[300px] lg:max-w-[335px] xl:min-w-[290px] max-w-[294px] rounded-[12px] p-px [background:linear-gradient(160deg,#7FB8EF_0%,#1D1D29_50%,#7FB8EF_100%)] overflow-hidden mx-auto"
  >
    <div
      class="rounded-[12px] h-[188px] relative bg-gradient-to-bl from-[#181B2B] via-[#245164] to-[#181B2B] py-5 px-4 cursor-pointer"
      @click="handleClick"
    >
      <div class="w-full flex justify-between items-center mb-8">
        <div class="flex items-center gap-3">
          <!-- Token and Network Images -->
          <div class="relative w-[44px] h-[44px]">
            <img
              class="w-full h-full rounded-full coin-border"
              :src="tokenImg"
              alt="token-img"
            />
            <img
              class="absolute bottom-0 w-[16px] h-[16px] rounded-full"
              :src="networkImg"
              alt="network-img"
            />
          </div>

          <!-- Token Info -->
          <div>
            <h5 class="font-satoshiMedium text-[12px] leading-4 text-white">
              {{ tokenTicker || "N/A" }}
            </h5>
            <div class="relative group inline-block max-w-[155px]">
              <h5
                class="font-satoshiMedium text-[16px] leading-[22px] text-white truncate"
              >
                {{
                  formatUSDT(
                    divideNumberUsingDecimals(lot.token_price, USDT_DECIMALS)
                  )
                }}
                USDT
              </h5>
              <div
                v-if="showTooltip"
                class="tooltip-content absolute left-1/2 -translate-x-1/2 bottom-full mb-2 px-3 py-1 rounded bg-black text-white text-xs opacity-0 group-hover:opacity-100 pointer-events-none transition-opacity z-50"
              >
                {{ usdtValue }} USDT
              </div>
            </div>
          </div>
        </div>

        <!-- Access Status Icon -->
        <div
          class="flex items-center justify-center bg-[#23484c] w-[32px] h-[32px] rounded-full"
        >
          <inline-svg
            :src="hasAccess ? unlockGreenIcon : lockRedIcon"
            class="w-5 h-5"
          ></inline-svg>
        </div>
      </div>

      <!-- Lot Details -->
      <div>
        <div class="flex justify-between items-center mb-3">
          <span class="text-[12px] leading-4 text-white/50 font-satoshiMedium">
            Pool Type
          </span>
          <span class="text-[12px] leading-4 text-white font-satoshiMedium">
            Private
          </span>
        </div>

        <div class="flex justify-between items-center mb-3">
          <span class="text-[12px] leading-4 text-white/50 font-satoshiMedium">
            Participants
          </span>
          <span class="text-[12px] leading-4 text-white font-satoshiMedium">
            {{ participants }}
          </span>
        </div>

        <div class="flex justify-between items-center">
          <span class="text-[12px] leading-4 text-white/50 font-satoshiMedium">
            Unlock Start
          </span>
          <span class="text-[12px] leading-4 text-white font-satoshiMedium">
            {{ unlockStart ? $dayjs(unlockStart).format("DD/MM/YYYY") : "N/A" }}
          </span>
        </div>
      </div>

      <!-- Background Wave -->
      <inline-svg
        :src="waveIcon"
        class="absolute top-[0px] left-[0px]"
      ></inline-svg>
    </div>
  </div>
</template>

<script setup>
import InlineSvg from "vue-inline-svg";
import unlockGreenIcon from "~/assets/images_new/icons/unlock-green.svg";
import lockRedIcon from "~/assets/images_new/icons/lock-red.svg";
import waveIcon from "~/assets/images_new/icons/wave.svg";
import { formatUSDT } from "~/utils/number";
import { computed } from "vue";
import { useRouter } from "vue-router";

const { $dayjs } = useNuxtApp();
const router = useRouter();

// Props destructuring for cleaner code
const {
  tokenImg,
  networkImg,
  tokenTicker,
  tokenPrice,
  hasAccess,
  participants,
  unlockStart,
  lot,
} = defineProps({
  tokenImg: {
    type: String,
    required: true,
  },
  networkImg: {
    type: String,
    required: true,
  },
  tokenTicker: {
    type: String,
    default: "N/A",
  },
  tokenPrice: {
    type: String,
    default: "0",
  },
  hasAccess: {
    type: Boolean,
    default: false,
  },
  participants: {
    type: Number,
    default: 0,
  },
  unlockStart: {
    type: String,
    default: null,
  },
  lot: {
    type: Object,
    default: null,
  },
  networkSymbol: {
    type: String,
    default: "N/A",
  },
});

const emit = defineEmits(["showBuyModalEvent"]);

const usdtValue = computed(() => formatUSDT(tokenPrice) || "0.0000");
const showTooltip = computed(() => usdtValue.value.length > 12);

const handleClick = () => {
  const lotPath = `/${lot.plan_id}-${lot.display_id}-${lot.list_id}?network=${lot.network_symbol}`;
  router.push(lotPath);
};
</script>

<style scoped>
.card-lot:hover .bg-backdrop {
  opacity: 1;
  z-index: 50;
}

.tooltip-content {
  white-space: nowrap;
}
</style>

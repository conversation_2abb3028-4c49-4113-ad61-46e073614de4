<template>
  <div class="flex flex-col gap-2">
    <div
      v-for="plan in token.plans"
      :key="plan.display_id"
      class="card-lot relative bg-white/[6%] rounded-xl group"
    >
      <div class="relative collapse">
        <input
          @click.stop="toggleDropdown(index)"
          type="checkbox"
          class="w-12 h-10"
          :class="
            isOpen[index]
              ? 'absolute w-1/5 top-16 right-0'
              : 'absolute w-1/5 bottom-0 right-0'
          "
        />
        <div class="collapse-title pl-4 p-5 h-[142px]">
          <div>
            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center gap-4">
                <div class="relative w-[44px] h-[44px] flex-shrink-0">
                  <img
                    class="w-full h-full rounded-full coin-border"
                    :src="token.token_image"
                    alt="token-img"
                  />

                  <img
                    class="absolute bottom-0 w-[16px] h-[16px]"
                    :src="token.network_image"
                    alt="network-img"
                  />
                </div>
                <div class="flex flex-col">
                  <h5 class="text-white text-2sm">{{ token.token_ticker }}</h5>
                  <h5 class="text-sm text-input-icons">
                    {{ token.token_name }}
                  </h5>
                </div>
              </div>
              <div class="dropdown dropdown-end" @click.stop.prevent="">
                <div tabindex="0" role="button" class="flex justify-center">
                  <inline-svg class="w-1 h-5" :src="optionsIcon"></inline-svg>
                </div>
                <ul
                  tabindex="0"
                  class="dropdown-content text-2sm bg-base-100 py-3 h-[90px] overflow-y-auto overflow-x-hidden rounded-[8px] z-[1] w-[110px] border border-textbox-border shadow-[0_0_4px_0_#353333] text-white mr-2 -mt-0.5"
                >
                  <li
                    v-if="Object.keys(claimableAmounts).length > 0"
                    class="transition-colors duration-200 cursor-pointer hover:bg-white/10"
                    :class="{
                      disabled:
                        claimableAmounts[plan.vesting_id]?.tokenAmount * 1 == 0,
                    }"
                    @click="
                      claimableAmounts[plan.vesting_id]?.tokenAmount * 1 !== 0
                        ? $emit('claimEvent', {
                            plan: {
                              ...plan,
                              token_image: token.token_image,
                            },
                          })
                        : null
                    "
                  >
                    <a class="pl-3 h-[30px] flex items-center"> Claim </a>
                  </li>
                  <li
                    class="transition-colors duration-200 cursor-pointer hover:bg-white/10"
                    @click="
                      $emit('listEvent', {
                        plan: {
                          ...plan,
                          token_image: token.token_image,
                          token_ticker: token.token_ticker,
                          token_price: token.price,
                          token_decimal: token.token_decimal,
                        },
                      })
                    "
                  >
                    <a class="pl-3 h-[30px] flex items-center">List</a>
                  </li>
                  <li
                    class="transition-colors duration-200 cursor-pointer hover:bg-white/10"
                    @click="
                      $emit('summaryEvent', {
                        plan: {
                          ...plan,
                          token_image: token.token_image,
                          token_ticker: token.token_ticker,
                          token_price: token.price,
                          token_decimal: token.token_decimal,
                        },
                      })
                    "
                  >
                    <a class="pl-3 h-[30px] flex items-center">Summary</a>
                  </li>
                </ul>
              </div>
            </div>
            <div class="text-white mb-1 text-[18px] leading-[22px]">
              {{
                formatUSDT(
                  divideNumberUsingDecimals(
                    token.price,
                    USDT_DECIMALS
                  ).multipliedBy(
                    divideNumberUsingDecimals(
                      plan.raw_amount,
                      token.token_decimal
                    )
                  )
                )
              }}
              <!-- {{
              numeral(
                web3Store.formatNumber(token.price, token.decimal) *
                  web3Store.formatNumber(plan.raw_amount)
              ).format("0,0.0000", Math.floor)
            }} -->
              USDT
            </div>
            <div v-if="Object.keys(claimableAmounts).length == 0">
              <div class="flex justify-start">
                <span class="loading loading-spinner loading-sm"></span>
              </div>
            </div>
            <div v-else>
              <div class="flex justify-between">
                <div class="text-sm text-input-icons">
                  Claimable
                  <span class="text-success">
                    {{
                      formatUSDT(
                        claimableAmounts[plan.vesting_id]?.tokenAmount || 0
                      )
                    }}
                  </span>
                </div>
                <label for="index">
                  <inline-svg
                    width="14"
                    :src="chevronDown"
                    :style="{
                      transform: isOpen[index]
                        ? 'rotate(180deg)'
                        : 'rotate(0deg)',
                      transition: 'transform 0.3s ease',
                    }"
                  />
                </label>
              </div>
            </div>
          </div>
          <!-- {{ token }} -->
        </div>
        <div class="text-sm collapse-content">
          <div class="flex flex-col gap-3">
            <div class="flex justify-between">
              <h5 class="text-input-icons">Plan Name</h5>
              <div class="text-white">
                {{ plan.plan_name }}
              </div>
            </div>
            <div class="flex justify-between">
              <h5 class="text-input-icons">Sell Limit</h5>
              <div class="text-white">
                {{
                  formatToken(
                    getSellable(
                      divideNumberUsingDecimals(
                        plan.total_allocated ?? 0,
                        token.token_decimal
                      ).toString(),
                      divideNumberUsingDecimals(
                        plan.market_amount ?? 0,
                        token.token_decimal
                      ).toString(),
                      divideNumberUsingDecimals(
                        plan.claimed_amount ?? 0,
                        token.token_decimal
                      ).toString(),
                      divideNumberUsingDecimals(
                        plan.selling_limit ?? 0,
                        token.token_decimal
                      ).toString(),
                      divideNumberUsingDecimals(
                        plan.listed_amount ?? 0,
                        token.token_decimal
                      ).toString()
                    )
                  )
                }}
              </div>
            </div>
            <div class="flex justify-between">
              <h5 class="text-input-icons">Next Unlock</h5>
              <div class="text-white">
                {{ calcNextUnlock(plan) }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import chevronDown from "~/assets/images_new/icons/chevron-down.svg";
import optionsIcon from "~/assets/images_new/icons/options.svg";

import InlineSvg from "vue-inline-svg";
import { TIME_UNIT, USDT_DECIMALS } from "~/utils/const";
import {
  divideNumberUsingDecimals,
  formatUSDT,
  formatToken,
} from "~/utils/number";

const { $dayjs } = useNuxtApp();
const timeUnit = TIME_UNIT;

const props = defineProps({
  token: Object,
  claimableAmounts: {
    type: Object,
    default: null,
  },
  index: Number,
});

const isOpen = ref([]);

function toggleDropdown(index) {
  isOpen.value[index] = !isOpen.value[index];
}

function getSellable(
  totalAllocated,
  marketAmount,
  claimedAmount,
  sellLimit
) {
  const claimable = totalAllocated * 1 + marketAmount * 1 - claimedAmount * 1;

  return claimable > sellLimit * 1 ? sellLimit * 1 : claimable;
}

function calcNextUnlock(plan) {
  const currentTime = $dayjs.utc();

  if (plan.function == "TIME") {
    if (
      $dayjs
        .utc(Number(plan.start))
        .add(plan.cliff_duration, timeUnit)
        .isAfter(currentTime)
    ) {
      return $dayjs
        .utc(Number(plan.start))
        .add(plan.cliff_duration, timeUnit)
        .format("YYYY-MM-DD");
    } else {
      return "Time-based";
    }
  } else {
    if ($dayjs.utc(Number(plan.start)).isAfter(currentTime)) {
      return $dayjs.utc(Number(plan.start)).format("YYYY-MM-DD");
    } else if ($dayjs.utc(Number(plan.end)).isBefore(currentTime)) {
      return "---";
    } else {
      // Calculate cycle duration and completed cycles
      const cycleDuration = plan.duration; // duration in days
      const startDate = $dayjs.utc(Number(plan.start));
      const daysSinceStart = currentTime.diff(startDate, timeUnit);
      const completedCycles = Math.floor(daysSinceStart / cycleDuration);

      // Calculate next unlock time
      const nextUnlockTime = startDate.add(
        (completedCycles + 1) * cycleDuration,
        timeUnit
      );

      // Check if next unlock is after end date
      if (nextUnlockTime.isAfter($dayjs.utc(Number(plan.end)))) {
        return "---";
      }

      return nextUnlockTime.format("YYYY-MM-DD");
    }
  }
}
</script>

<style scoped></style>

<template>
  <div class="flex items-center justify-center h-[500px]">
    <div class="flex flex-col gap-8 items-center">
      <img :src="noInfoIcon" class="w-[132px]" />
      <h5 class="font-medium text-xl text-white">{{ content }}</h5>
      <h5
        v-if="subcontent"
        class="font-medium font-satoshiMedium leading-4 text-[#84858E] mt-4"
      >
        {{ subcontent }}
      </h5>

      <button
        v-if="redirectButton"
        @click="goToMarketplace"
        class="min-w-[263px] btn btn-primary text-2sm min-h-10 max-h-10 w-full cursor-pointer hover:opacity-80"
      >
        Go To Marketplace
      </button>
    </div>
  </div>
</template>

<script setup>
import noDataIcon from "~/assets/images_new/marketplace/no_data.webp";
import noHistoryIcon from "~/assets/images_new/marketplace/no_history.webp";

const props = defineProps({
  redirectButton: {
    type: Boolean,
    default: false,
  },
  content: String,
  subcontent: String || null,
  isHistory: {
    type: Boolean,
    default: false,
  },
});

const noInfoIcon = computed(() => {
  return props.isHistory ? noHistoryIcon : noDataIcon;
});

const goToMarketplace = () => {
  navigateTo("/");
};
</script>

<style scoped></style>

<template>
  <transition mode="out-in" name="fade">
    <div v-if="isLoading">
      <SkeletonMarketplaceToken />
    </div>
    <div v-else>
      <div
        class="relative flex justify-center transition-all duration-200 ease-in-out"
      >
        <transition name="fade">
          <div
            v-if="isCopied"
            class="absolute -top-5 flex justify-center items-center gap-2 bg-[#50F18712] text-[#50F187] text-2sm w-[155px] h-[44px] rounded-[8px]"
          >
            <inline-svg
              width="16"
              height="16"
              :src="checkCircleIcon"
            ></inline-svg>
            <h6>Link Copied</h6>
          </div>
        </transition>
      </div>

      <div class="hidden lg:block">
        <div class="relative flex items-center justify-between mb-10">
          <nuxt-link to="/">
            <div class="flex items-center gap-1">
              <inline-svg :src="chevronLeftIcon"></inline-svg>
              <h5 class="text-sm">Back</h5>
            </div>
          </nuxt-link>
          <div>
            <button
              :data-clipboard-text="link"
              class="copy-link btn border-primary bg-transparent text-primary h-8 min-h-8 !rounded-[4px] font-medium hover:border-primary hover:opacity-80"
            >
              <inline-svg :src="shareIcon"></inline-svg>
              <h5>Share</h5>
            </button>
          </div>
        </div>
      </div>

      <div class="flex flex-col gap-5 lg:flex-row mb-14">
        <div class="lg:w-[47%]">
          <div class="bg-white/[6%] p-5 lg:p-8 rounded-xl mb-5">
            <div
              class="flex flex-col items-start gap-5 pb-6 border-b lg:gap-4 border-white/10 lg:flex-row lg:items-center"
            >
              <div class="flex items-center justify-between w-full lg:w-fit">
                <img
                  class="max-w-[48px] h-[48px] lg:min-w-[90px] lg:h-[90px] rounded-full coin-border"
                  width="90px"
                  height="90px"
                  :src="tokenInfo.token_image"
                  alt=""
                />
                <inline-svg
                  :data-clipboard-text="link"
                  :src="shareIcon"
                  class="w-5 h-5 lg:hidden copy-link"
                ></inline-svg>
              </div>
              <div class="lg:w-full">
                <h3 class="font-medium text-white text-2sm lg:text-lg">
                  {{ tokenInfo.token_name }}
                  <span class="text-sm font-medium text-white/50">{{
                    tokenInfo.token_ticker
                  }}</span>
                </h3>
                <div class="flex items-end gap-2 mb-1">
                  <h1
                    class="text-[18px] lg:text-2xl font-satoshiBlack text-white"
                  >
                    {{
                      formatUSDT(
                        divideNumberUsingDecimals(
                          tokenInfo.token_price,
                          USDT_DECIMALS
                        )
                      )
                    }}
                    USD
                  </h1>
                  <div class="flex items-center gap-2 mb-1">
                    <inline-svg
                      :class="`${common.trendingColor(
                        tokenInfo.twf_hour_changes * 1 > 0
                      )}`"
                      :src="trendingDown"
                    >
                    </inline-svg>
                    <h5
                      :class="`${common.twfColor(
                        tokenInfo.twf_hour_changes * 1 > 0
                      )} text-sm lg:text-2sm`"
                    >
                      {{ common.twfSymbol(tokenInfo.twf_hour_changes * 1 > 0)
                      }}{{ tokenInfo.twf_hour_changes }}% (24H)
                    </h5>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <img
                    class="w-[16px] h-[16px]"
                    width="16px"
                    height="16px"
                    :src="tokenInfo.network_image"
                    alt="network-icon"
                  />

                  <nuxt-link
                    :href="`${useRuntimeConfig().public.explorerUrl}/address/${
                      tokenInfo.token_address
                    }`"
                    target="_blank"
                  >
                    <h5 class="flex items-center gap-1 text-2sm text-primary">
                      {{ common.formatAddress(tokenInfo.token_address) }}
                      <inline-svg
                        class="w-4 h-4 stroke-primary"
                        :src="arrowUpRightIcon"
                      ></inline-svg>
                    </h5>
                  </nuxt-link>
                </div>
              </div>
            </div>
            <div class="flex gap-14 sm:justify-between pt-[25px] md:pt-6">
              <div>
                <h5 class="text-sm text-white">Best Price</h5>
                <h3 class="text-base md:text-lg text-success">
                  {{
                    formatToken(
                      divideNumberUsingDecimals(
                        tokenInfo.best_price,
                        USDT_DECIMALS
                      )
                    )
                  }}
                  USDT
                </h3>
              </div>
              <div>
                <h5 class="text-sm text-white">Total Available</h5>
                <h3 class="text-base md:text-lg text-success">
                  {{
                    formatToken(
                      divideNumberUsingDecimals(
                        tokenInfo.total_available,
                        tokenInfo.token_decimal
                      )
                    )
                  }}
                </h3>
              </div>
              <div class="hidden xs:block">
                <h5 class="text-sm text-white">Discount to Spot</h5>
                <h3 class="text-base md:text-lg text-success">
                  Up To {{ formatToken(tokenInfo.max_discount) }}%
                </h3>
              </div>
            </div>
            <div class="mt-6 xs:hidden">
              <h5 class="text-sm text-white">Discount to Spot</h5>
              <h3 class="text-base md:text-lg text-success">
                Up To {{ formatToken(tokenInfo.max_discount) }}%
              </h3>
            </div>
          </div>
          <div class="relative">
            <div
              class="collapse lg:p-8 rounded-xl lg:collapse-open"
              :class="
                Number(tokenInfo.market_cap) === 0 &&
                Number(tokenInfo.max_supply) === 0 &&
                Number(tokenInfo.token_price) === 0 &&
                Number(tokenInfo.twf_hour_trading) === 0 &&
                Number(tokenInfo.total_supply) === 0
                  ? 'bg-transparent'
                  : 'bg-white/[6%]'
              "
            >
              <input
                @click.stop="marketDropdown"
                type="checkbox"
                class="lg:min-h-fit lg:max-h-fit"
              />
              <div
                :class="
                  Number(tokenInfo.market_cap) === 0 &&
                  Number(tokenInfo.max_supply) === 0 &&
                  Number(tokenInfo.token_price) === 0 &&
                  Number(tokenInfo.twf_hour_trading) === 0 &&
                  Number(tokenInfo.total_supply) === 0
                    ? !marketOpen
                      ? 'bg-white/[6%] lg:bg-transparent'
                      : 'bg-white/[6%] lg:bg-transparent'
                    : 'lg:bg-transparent'
                "
                class="collapse-title h-[52px] text-xs tracking-[3px] text-white flex items-center px-5 lg:p-0 lg:min-h-fit lg:max-h-fit lg:mb-6"
              >
                <div class="flex items-center justify-between w-full">
                  <h6>MARKET</h6>
                  <inline-svg
                    class="lg:hidden"
                    width="16"
                    :src="chevronDown"
                    :style="{
                      transform: marketOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                      transition: 'transform 0.3s ease',
                    }"
                  />
                </div>
              </div>
              <div class="collapse-content lg:p-0">
                <div
                  :class="
                    Number(tokenInfo.market_cap) === 0 &&
                    Number(tokenInfo.max_supply) === 0 &&
                    Number(tokenInfo.token_price) === 0 &&
                    Number(tokenInfo.twf_hour_trading) === 0 &&
                    Number(tokenInfo.total_supply) === 0
                      ? 'block'
                      : 'hidden'
                  "
                  class="bg-white/[6%] absolute rounded-b-xl md:rounded-xl w-full top-[60px] h-[246px] md:top-0 md:h-[329px] left-0"
                >
                  <div
                    class="flex flex-col h-[306px] md:h-full justify-center items-center gap-[13px] -mt-[60px] md:mt-0"
                  >
                    <inline-svg
                      :src="noMarketDataIcon"
                      class="w-8 h-8"
                    ></inline-svg>
                    <h3>No Available Data</h3>
                  </div>
                </div>

                <div
                  class="flex flex-col gap-5 px-1 pb-1 mt-0 text-2sm lg:pb-0 lg:px-0"
                  :class="
                    Number(tokenInfo.market_cap) === 0 &&
                    Number(tokenInfo.max_supply) === 0 &&
                    Number(tokenInfo.token_price) === 0 &&
                    Number(tokenInfo.twf_hour_trading) === 0 &&
                    Number(tokenInfo.total_supply) === 0
                      ? 'filter blur-sm'
                      : 'filter-none'
                  "
                >
                  <div class="flex justify-between">
                    <h4 class="text-input-icons">Market Cap</h4>
                    <h4 class="text-white">
                      {{
                        tokenInfo.market_cap && tokenInfo.market_cap !== "0"
                          ? "$" +
                            numeral(
                              divideNumberUsingDecimals(
                                tokenInfo.market_cap,
                                tokenInfo.token_decimal
                              ).toString()
                            ).format("0,0")
                          : "--------"
                      }}
                    </h4>
                  </div>
                  <div class="flex justify-between">
                    <h4 class="text-input-icons">Fully Diluted Valuation</h4>
                    <h4 class="text-white">
                      {{
                        tokenInfo.max_supply &&
                        tokenInfo.token_price &&
                        tokenInfo.max_supply !== "0" &&
                        tokenInfo.token_price !== "0"
                          ? "$" +
                            numeral(
                              divideNumberUsingDecimals(
                                tokenInfo.max_supply,
                                tokenInfo.token_decimal
                              )
                                .multipliedBy(
                                  divideNumberUsingDecimals(
                                    tokenInfo.token_price,
                                    USDT_DECIMALS
                                  )
                                )
                                .toString()
                            ).format("0,0")
                          : "--------"
                      }}
                    </h4>
                  </div>

                  <div class="flex justify-between">
                    <h4 class="text-input-icons">24h Trading Volume</h4>
                    <h4 class="text-white">
                      {{
                        tokenInfo.twf_hour_trading &&
                        tokenInfo.twf_hour_trading !== "0"
                          ? numeral(
                              divideNumberUsingDecimals(
                                tokenInfo.twf_hour_trading,
                                tokenInfo.token_decimal
                              ).toString()
                            ).format("0,0")
                          : "--------"
                      }}
                    </h4>
                  </div>

                  <div class="flex justify-between">
                    <h4 class="text-input-icons">Circulating Supply</h4>
                    <h4 class="text-white">
                      {{
                        tokenInfo.market_cap &&
                        tokenInfo.token_price &&
                        tokenInfo.market_cap !== "0" &&
                        tokenInfo.token_price !== "0"
                          ? numeral(
                              divideNumberUsingDecimals(
                                tokenInfo.market_cap,
                                USDT_DECIMALS
                              )
                                .dividedBy(
                                  divideNumberUsingDecimals(
                                    tokenInfo.token_price,
                                    USDT_DECIMALS
                                  )
                                )
                                .toString()
                            ).format("0,0")
                          : "--------"
                      }}
                    </h4>
                  </div>

                  <div class="flex justify-between">
                    <h4 class="text-input-icons">Total Supply</h4>
                    <h4 class="text-white">
                      {{
                        tokenInfo.total_supply && tokenInfo.total_supply !== "0"
                          ? numeral(
                              divideNumberUsingDecimals(
                                tokenInfo.total_supply,
                                tokenInfo.token_decimal
                              ).toString()
                            ).format("0,0")
                          : "--------"
                      }}
                    </h4>
                  </div>

                  <div class="flex justify-between">
                    <h4 class="text-input-icons">Max Supply</h4>
                    <h4 class="text-white">
                      {{
                        tokenInfo.max_supply && tokenInfo.max_supply !== "0"
                          ? numeral(
                              divideNumberUsingDecimals(
                                tokenInfo.max_supply,
                                tokenInfo.token_decimal
                              ).toString()
                            ).format("0,0")
                          : "--------"
                      }}
                    </h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="w-full lg:w-[53%] flex flex-col">
          <div
            class="collapse mb-5 lg:p-8 rounded-xl bg-white/[6%] lg:collapse-open lg:min-h-[400px] lg:h-[400px]"
          >
            <input
              @click.stop="aboutDropdown"
              type="checkbox"
              class="lg:min-h-fit lg:max-h-fit"
            />
            <div
              class="collapse-title h-[52px] text-xs tracking-[3px] text-white flex items-center px-5 py-0 lg:p-0 lg:min-h-fit lg:max-h-fit lg:mb-6"
            >
              <div class="flex items-center justify-between w-full">
                <h6>ABOUT</h6>
                <inline-svg
                  width="16"
                  class="lg:hidden"
                  :src="chevronDown"
                  :style="{
                    transform: aboutOpen ? 'rotate(180deg)' : 'rotate(0deg)',
                    transition: 'transform 0.3s ease',
                  }"
                />
              </div>
            </div>
            <div class="collapse-content md:p-0">
              <div
                class="flex flex-col gap-8 px-1 text-2sm md:px-5 lg:pb-0 lg:px-0"
              >
                <p
                  v-if="!tokenInfo.onchain_info.description"
                  class="relative text-2sm text-white/50 line-clamp-5 min-w-[84px]"
                  id="tokenDesc"
                >
                  {{
                    isClickedReadMore
                      ? tokenInfo.description
                      : tokenInfo.description.slice(0, 300)
                  }}
                  <button
                    type="button"
                    v-if="
                      !isClickedReadMore && tokenInfo.description.length > 300
                    "
                    @click="isClickedReadMore = true"
                    class="px-4 text-sm cursor-pointer text-primary bg-secondary"
                  >
                    Read more
                  </button>
                </p>
                <p
                  v-else
                  class="relative text-2sm text-white/50 line-clamp-5 min-w-[84px]"
                  id="tokenDesc"
                >
                  {{
                    isClickedReadMore
                      ? tokenInfo.onchain_info.description
                      : tokenInfo.onchain_info.description.slice(0, 300)
                  }}
                  <button
                    type="button"
                    v-if="
                      !isClickedReadMore &&
                      tokenInfo.onchain_info.description.length > 300
                    "
                    @click="isClickedReadMore = true"
                    class="px-4 text-sm cursor-pointer text-primary bg-secondary"
                  >
                    Read more
                  </button>
                </p>

                <div>
                  <h6 class="text-xs tracking-[3px] text-white mb-5">
                    CATEGORIES
                  </h6>
                  <div class="flex flex-wrap gap-3 text-2sm">
                    <template v-if="visibleCategories.length > 0">
                      <div
                        class="bg-info p-1 min-w-[70px] px-5 text-center rounded-[4px]"
                        v-for="category in visibleCategories.slice(0, 2)"
                      >
                        {{ category }}
                      </div>

                      <div
                        class="dropdown dropdown-top"
                        v-if="
                          visibleCategories.length > 2 ||
                          hiddenCategories.length > 0
                        "
                      >
                        <div
                          tabindex="0"
                          role="button"
                          class="flex justify-center items-center gap-1 bg-info p-1 min-w-[70px] px-2 text-center rounded-[4px]"
                        >
                          <span>More</span>
                          <inline-svg :src="dropdownIcon"></inline-svg>
                        </div>
                        <ul
                          tabindex="0"
                          class="dropdown-content menu p-4 text-2sm bg-info mt-1 rounded-[4px] z-[10] w-52 shadow gap-2"
                        >
                          <li
                            v-for="category in [
                              visibleCategories.slice(2),
                              ...hiddenCategories,
                            ]"
                          >
                            {{ category }}
                          </li>
                        </ul>
                      </div>
                    </template>
                    <div v-else class="text-white/50 text-2sm">
                      No available data
                    </div>
                  </div>
                </div>

                <div>
                  <h6 class="text-xs tracking-[3px] text-white mb-5">
                    INVESTORS
                  </h6>
                  <div class="flex flex-wrap gap-3 text-2sm">
                    <template v-if="investors && investors.length > 0">
                      <div
                        class="bg-info px-5 p-1 min-w-[120px] text-center rounded-[4px]"
                        v-for="investor in investors.slice(0, 2)"
                      >
                        {{ investor.split(" Portfolio").at(0) }}
                      </div>
                    </template>
                    <div
                      class="dropdown dropdown-top"
                      v-if="investors && investors.length > 2"
                    >
                      <div
                        tabindex="0"
                        role="button"
                        class="flex justify-center items-center gap-1 bg-info p-1 min-w-[70px] px-2 text-center rounded-[4px]"
                      >
                        <span>More</span>
                        <inline-svg :src="dropdownIcon"></inline-svg>
                      </div>
                      <ul
                        tabindex="0"
                        class="dropdown-content menu p-4 text-2sm bg-info mt-1 rounded-[4px] z-[10] w-52 shadow gap-2"
                      >
                        <li v-for="investor in investors.slice(2)">
                          {{ investor.split(" Portfolio").at(0) }}
                        </li>
                      </ul>
                    </div>
                    <div v-else class="text-white/50 text-2sm">
                      No available data
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            class="bg-white/[6%] p-5 md:p-8 rounded-xl h-full flex flex-col justify-center"
          >
            <div class="flex items-center justify-between mb-8">
              <h6 class="text-xs tracking-[3px] text-white">WEBSITE</h6>
              <template v-if="tokenInfo.onchain_info.websites">
                <nuxt-link
                  :href="tokenInfo.onchain_info.websites"
                  target="_blank"
                >
                  <h6 class="flex items-center gap-1 text-base text-primary">
                    {{ common.getDomain(tokenInfo.onchain_info.websites) }}
                    <inline-svg
                      class="stroke-primary"
                      :src="arrowUpRightIcon"
                    ></inline-svg>
                  </h6>
                </nuxt-link>
              </template>
              <div v-else class="text-white/50 text-2sm">No available data</div>
            </div>

            <div class="flex items-center justify-between">
              <h6 class="text-xs tracking-[3px] text-white">LINKS</h6>
              <template v-if="Object.keys(socialMedia).length > 0">
                <div class="flex gap-2">
                  <nuxt-link
                    target="_blank"
                    :href="`${common.socialUrlPrefix[key]}${url}`"
                    class="h-[32px] w-[32px] bg-primary/20 flex justify-center items-center rounded-full"
                    v-for="(url, key) in socialMedia"
                  >
                    <inline-svg
                      class="fill-primary"
                      :src="getIconPath(key)"
                    ></inline-svg>
                  </nuxt-link>
                </div>
              </template>
              <div v-else class="text-white/50 text-2sm">No available data</div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h2 class="mb-4 text-xl text-white md:text-2xl">Available Lots</h2>
        <div
          class="flex items-center justify-between pb-6 lg:border-b lg:border-white/20"
        >
          <div class="flex gap-10 text-2sm">
            <h5
              :class="`${
                activeTab == 'bestDeal'
                  ? 'tab-active relative'
                  : 'text-white/50'
              } relative cursor-pointer`"
              @click="activeTab = 'bestDeal'"
            >
              Best Deal
            </h5>

            <h5
              :class="`${
                activeTab == 'unlockingSoon'
                  ? 'tab-active relative'
                  : 'text-white/50'
              } relative cursor-pointer`"
              @click="activeTab = 'unlockingSoon'"
            >
              Unlocking Soon
            </h5>
          </div>

          <div
            class="flex items-center justify-center gap-2 rounded-full border border-white/20 lg:px-4 w-8 h-8 lg:w-[220px] lg:h-[40px]"
          >
            <inline-svg
              :src="searchIcon"
              class="hidden w-4 h-4 lg:block min-w-4 stroke-input-icons"
            ></inline-svg>
            <inline-svg
              @click="openSearchModal"
              :src="searchIcon"
              class="w-4 h-4 lg:hidden min-w-4 stroke-white"
            ></inline-svg>
            <input
              class="hidden lg:block input w-[90%] h-8 min-h-8 bg-transparent border-none focus:ring-0 focus:outline-none p-0 text-2sm"
              type="text"
              placeholder="Search for lot number"
              @keyup="searchLots"
              v-model="searchTerm"
            />
          </div>
        </div>

        <div class="hidden py-4 lg:block">
          <table
            class="table border-separate table-fixed text-2sm border-spacing-y-3"
          >
            <thead class="text-sm font-normal">
              <tr class="border-b-0">
                <th class="w-[22%]">Lot</th>
                <th class="w-[15%]">
                  <div class="flex">
                    <h5>Price</h5>
                    <inline-svg
                      class="w-[16px] h-[16px]"
                      :src="sortIcon"
                    ></inline-svg>
                  </div>
                </th>
                <th class="w-[15%]">
                  <div class="flex">
                    <h5>Best Price</h5>
                    <inline-svg
                      class="w-[16px] h-[16px]"
                      :src="sortIcon"
                    ></inline-svg>
                  </div>
                </th>
                <th class="w-[15%]">
                  <div class="flex">
                    <h5>Discount</h5>
                    <inline-svg
                      class="w-[16px] h-[16px]"
                      :src="sortIcon"
                    ></inline-svg>
                  </div>
                </th>
                <th class="w-[16%]">
                  <div class="flex">
                    <h5>Unlock Start</h5>
                    <inline-svg
                      class="w-[16px] h-[16px]"
                      :src="sortIcon"
                    ></inline-svg>
                  </div>
                </th>
                <th class="w-[7%] text-right p-0 font-normal">% Filled</th>
                <th class="w-[14%] text-right pr-2"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="!lots || lots.length == 0">
                <td colspan="7">
                  <NoData :content="'No Lots Available'"></NoData>
                </td>
              </tr>
              <tr
                v-else
                v-for="(lot, index) in lots"
                class="transition-all duration-300 border-b-0 cursor-pointer hover:bg-white/10"
                @click="goToLotDetails(lot)"
              >
                <td class="rounded-s-lg">
                  <div class="flex items-center gap-4">
                    <div class="relative w-[44px] h-[44px] flex-shrink-0">
                      <img
                        class="w-full h-full rounded-full coin-border"
                        :src="lot.token_image"
                        alt="token-img"
                      />

                      <img
                        class="absolute bottom-0 w-[16px] h-[16px]"
                        :src="lot.network_image"
                        alt="network-img"
                      />
                    </div>

                    <div>
                      <h3>{{ lot.token_ticker }}</h3>
                      <div class="flex items-center gap-1">
                        <h5
                          class="text-sm text-white/50 truncate max-w-[50px] 2lg:truncate-none 2lg:max-w-full"
                        >
                          {{ lot.token_name }}
                        </h5>
                        <h5 class="text-xs text-white/50 font-satoshiLight">
                          #{{ lot.display_id }}
                        </h5>
                      </div>
                    </div>
                  </div>
                </td>

                <td>
                  <h5>
                    {{
                      formatUSDT(
                        divideNumberUsingDecimals(
                          lot.token_price,
                          USDT_DECIMALS
                        )
                      )
                    }}
                    USDT
                  </h5>
                  <h5
                    :class="`text-sm ${common.twfColor(
                      lot.twf_hour_changes * 1 > 0
                    )}`"
                  >
                    {{ common.twfSymbol(lot.twf_hour_changes * 1 > 0)
                    }}{{ lot.twf_hour_changes }}%
                  </h5>
                </td>

                <td>
                  <h5>
                    {{
                      formatUSDT(
                        divideNumberUsingDecimals(
                          lot.listed_price,
                          USDT_DECIMALS
                        ).multipliedBy(
                          toBigNumber(1).minus(
                            toBigNumber(lot.discount_pct).dividedBy(100)
                          )
                        )
                      )
                    }}
                    USDT
                  </h5>
                </td>

                <td>
                  <h5>
                    Up to
                    {{ formatToken(lot.discount_pct) }}%
                  </h5>
                </td>

                <td class="whitespace-nowrap">
                  <h5
                    v-if="
                      lot.start_date != 'undefined' &&
                      lot.cliff_duration != 'undefined'
                    "
                  >
                    {{
                      lot.vesting_type === "CYCLE"
                        ? $dayjs(lot.start_date)
                            .add(lot.duration, timeUnit)
                            .format("DD/MM/YYYY")
                        : $dayjs(lot.start_date).format("DD/MM/YYYY")
                    }}

                    <span class="text-xs text-white/50">{{
                      getCountDown(
                        lot.vesting_type === "CYCLE"
                          ? $dayjs(lot.start_date)
                              .add(lot.cliff_duration, timeUnit)
                              .add(lot.duration, timeUnit)
                              .format("x") * 1
                          : $dayjs(lot.start_date)
                              .add(lot.cliff_duration, timeUnit)
                              .format("x") * 1
                      )
                    }}</span>
                  </h5>
                </td>
                <td class="p-0 text-right whitespace-nowrap">
                  {{
                    lot.listing_type == 0
                      ? `${numeral(
                          ((lot.total_listing - lot.remaining_listing) /
                            lot.total_listing) *
                            100
                        ).format("0.00")}%`
                      : "Single Fill"
                  }}
                </td>
                <td class="text-right rounded-e-lg">
                  <button
                    @click.stop="
                      isLoggedIn ? showBuyModal(lot) : showLoginModal()
                    "
                    class="btn btn-primary h-[26px] min-h-[26px] min-w-[68px] !rounded-[4px] text-2sm font-medium"
                  >
                    Buy
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- mobile available lots -->
        <div v-if="!lots || lots.length == 0" class="lg:hidden">
          <NoData :content="'No Lots Available'"></NoData>
        </div>
        <div class="pb-4 lg:hidden">
          <div v-for="(lot, index) in lots" class="w-full pb-4">
            <CardAllLot
              @click="goToLotDetails(lot)"
              @showBuyModalEvent="
                isLoggedIn ? showBuyModal($event.lot || lot) : showLoginModal()
              "
              :index="index"
              :tokenImg="lot.token_image"
              :networkImg="lot.network_image"
              :networkSymbol="lot.network_symbol"
              :totalListing="lot.total_listing"
              :remainingListing="lot.remaining_listing"
              :tokenName="lot.token_name"
              :tokenTicker="lot.token_ticker"
              :tokenDecimal="lot.token_decimal"
              :listId="lot.list_id"
              :tokenPrice="
                divideNumberUsingDecimals(
                  lot.token_price,
                  USDT_DECIMALS
                ).toString()
              "
              :bestPrice="
                divideNumberUsingDecimals(
                  lot.listed_price,
                  USDT_DECIMALS
                ).toString()
              "
              :maxDiscount="lot.discount_pct"
              :unlockStart="lot.start_date ?? 0"
              :displayId="lot.display_id"
              :twfHourChanges="`${
                twfChangesInfo[lot.twf_hour_changes * 1 > 0].symbol
              }${lot.twf_hour_changes.replace('-', '')}%`"
              :twfHourColor="twfChangesInfo[lot.twf_hour_changes * 1 > 0].color"
              :lotId="
                common.formatLotId(
                  lot.token_ticker,
                  lot.display_id,
                  lot.list_id
                )
              "
              :buttonText="'Buy'"
            ></CardAllLot>
          </div>
        </div>
      </div>

      <!-- Read More Modal -->
      <dialog id="about_modal" class="modal">
        <div
          class="modal-box modal-container overflow-hidden bg-secondary max-h-[500px] overflow-y-auto p-8"
        >
          <form method="dialog">
            <button
              class="absolute text-xs btn btn-xs btn-circle btn-ghost right-6 top-5"
            >
              ✕
            </button>
          </form>
          <h6 class="mb-5 text-xs text-white">ABOUT</h6>
          <p
            class="text-2sm text-white/50 relative min-h-[100px]"
            id="tokenDesc"
          >
            {{ tokenInfo.onchain_info.description }}
          </p>
        </div>
      </dialog>

      <ModalMarketplaceBuy
        :lot="selectedLot"
        ref="buyModal"
        @openBuySummary="handleBuySummary"
      ></ModalMarketplaceBuy>

      <ModalBuySummary
        :lot="selectedLot"
        :buyForm="buyModal?.buyForm"
        :isMarketplaceBuy="true"
        :tokenImage="selectedLot?.token_image"
        :tokenPrice="selectedLot?.token_price || selectedLot?.price"
        :onBuySuccess="handleBuySuccess"
        ref="newBuySummaryModal"
      ></ModalBuySummary>
      <ModalConnect ref="connectModal"></ModalConnect>

      <ModalTokenDetailsSearch
        ref="searchTokenModalRef"
        v-model="isSearchModalVisible"
        @closeModal="closeSearchModal"
        @showBuyModal="showBuyModal"
        @showLoginModal="showLoginModal"
        :twfChangesInfo="twfChangesInfo"
        :lots="lots"
        :tokenAddress="tokenInfo.token_address"
        :sort_by="tabSortMap[activeTab.value]"
      ></ModalTokenDetailsSearch>
    </div>
  </transition>
</template>

<script setup>
import numeral from "numeral";
import InlineSvg from "vue-inline-svg";
import ClipboardJS from "clipboard";

import dropdownIcon from "~/assets/images_new/icons/dropdown.svg";
import arrowUpRightIcon from "~/assets/images_new/icons/arrow-up-right.svg";
import chevronLeftIcon from "~/assets/images_new/icons/chevron-left.svg";
import chevronDown from "~/assets/images_new/icons/chevron-down.svg";
import shareIcon from "~/assets/images_new/icons/share.svg";
import searchIcon from "~/assets/images_new/icons/search.svg";
import sortIcon from "~/assets/images_new/icons/sort.svg";
import trendingDown from "~/assets/images_new/icons/trending-down.svg";
import checkCircleIcon from "~/assets/images_new/icons/check-circle.svg";
import noMarketDataIcon from "~/assets/images_new/icons/no-token-info.svg";
import { USDT_DECIMALS, TIME_UNIT } from "~/utils/const";
import {
  divideNumberUsingDecimals,
  toBigNumber,
  formatUSDT,
  formatToken,
} from "~/utils/number";

definePageMeta({
  layout: "new-app",
});

const {
  tokenInfo,
  isLoading,
  isTruncated,
  investors,
  visibleCategories,
  hiddenCategories,
  socialMedia,
  lots,
  activeTab,
  twfChangesInfo,
  tabSortMap,
  getCountDown,
  getTokenLots,
} = useMarketplaceToken();

const route = useRoute();

const { $dayjs } = useNuxtApp();
const isCopied = ref(false);
const link = ref("");
const selectedLot = ref(null);
const buyModal = ref(null);
const newBuySummaryModal = ref(null);
const connectModal = ref(null);
const web3Store = useWeb3Store();
const newWeb3Store = useNewWeb3Store();
const { isLoggedIn } = storeToRefs(newWeb3Store);
const marketOpen = ref(false);
const aboutOpen = ref(false);
const searchTerm = ref(null);
const searchTokenModalRef = ref(null);
const isClickedReadMore = ref(false);

const isMobile = ref(false);
const timeUnit = TIME_UNIT;

let searchTimeout;

function getIconPath(socialMedia) {
  try {
    const url = new URL(
      `../../assets/images_new/icons/social_media/${socialMedia}.svg`,
      import.meta.url
    );

    console.log("img url", url);

    if (url.href.includes("undefined")) {
      throw "error";
    }

    return url.href;
  } catch (error) {
    console.error(
      `Error loading icon for socialMedia "${socialMedia}":`,
      error
    );
  }
}

function linkCopied() {
  isCopied.value = true;
  setTimeout(() => {
    isCopied.value = false;
  }, 2000);
}

function marketDropdown() {
  marketOpen.value = !marketOpen.value;
}

function aboutDropdown() {
  aboutOpen.value = !aboutOpen.value;
}

function detectWindowSize() {
  isMobile.value = window.innerWidth <= 375;
}

const isSearchModalVisible = ref(false);
function openSearchModal() {
  isSearchModalVisible.value = false;
  searchTerm.value = null;
  isSearchModalVisible.value = true;
}

async function closeSearchModal() {
  isSearchModalVisible.value = false;
  searchTokenModalRef.value.searchTerm = null;
}

async function searchLots() {
  // Clear any existing timeout
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  searchTimeout = setTimeout(async () => {
    await getTokenLots(tabSortMap[activeTab.value], searchTerm.value);
  }, 500);
}

async function toLotInfo(lot) {
  console.log("to lot", lot);

  router.push(
    `//${lot.plan_id}-${lot.display_id}-${lot.list_id}?network=${lot.network_symbol}`
  );
}

watch(activeTab, async (newValue, oldValue) => {
  if (newValue == "bestDeal") {
    await getTokenLots("BEST_DEAL");
    console.log("lots in token best deal", lots);
  } else if (newValue == "unlockingSoon") {
    await getTokenLots("UNLOCKING_SOON");
    console.log("lots in token unlock soon", lots);
  }
});

function showBuyModal(lot) {
  selectedLot.value = lot;
  buyModal.value.updateOutput(lot);
  buyModal.value.handleClick();
  console.log("selected lot", selectedLot.value);
  document.getElementById("buyModal").checked = true;
}

function showLoginModal() {
  connectModal.value.handleClick();
  document.getElementById("connectModal").checked = true;
}

function handleBuySummary() {
  console.log("handleBuySummary executed");

  newBuySummaryModal.value.handleClick();
}

async function handleBuySuccess() {
  lots.value = await getAllLots(
    tabSortMap[activeTab.value],
    order.value,
    searchTerm.value,
    undefined,
    filterNetwork.value
  );
}

function goToLotDetails(lot) {
  console.log("top token lot", lot);
  navigateTo(
    `/${lot.plan_id}-${lot.display_id}-${lot.list_id}?network=${lot.network_symbol}`
  );
}

onMounted(async () => {
  // check if token network is current connected network
  const tokenNetworkSymbol = route.query.network;
  await web3Store.switchNetwork(tokenNetworkSymbol);

  link.value = window.location.href;

  //clipboard
  var clipboard = new ClipboardJS(".copy-link");
  clipboard.on("success", function (e) {
    isCopied.value = true;
    setTimeout(() => {
      isCopied.value = false;
    }, 2000);
    e.clearSelection();
  });

  clipboard.on("error", function (e) {
    console.error("Action:", e.action);
    console.error("Trigger:", e.trigger);
  });

  detectWindowSize();
  window.addEventListener("resize", detectWindowSize);
});
</script>
<style lang="scss" scoped></style>

import { useAppKitNetwork } from '@reown/appkit/vue'
import { TIME_UNIT, USDT_DECIMALS } from '~/utils/const'
import { divideNumberUsingDecimals } from '~/utils/number'

export function useMyAssets() {
  const isLoading = ref(true)
  const activePlans = ref([])
  const claimedPlans = ref([])
  const tokenPlans = ref([])
  const userListedLots = ref([])
  const userListedLotsMeta = ref([])
  const userSummary = ref([])
  const { $dayjs } = useNuxtApp()
  const web3Store = useWeb3Store()
  const newWeb3Store = useNewWeb3Store()
  const { isLoggedIn, web3Kit } = storeToRefs(newWeb3Store)
  const { networksByChainId } = newWeb3Store
  const timeUnit = TIME_UNIT
  const router = useRouter()
  const selectedPlan = ref({})
  const claimableAmountSum = ref(0)
  const claimableAmounts = ref([])
  const currentNetwork = ref('ETH')
  const networkData = useAppKitNetwork()

  const multiUsdtAddress = {
    ETH: useRuntimeConfig().public.usdtAddress,
    AVAX: useRuntimeConfig().public.avaxUsdtAddress,
  }

  const multiMarketplaceAddress = {
    ETH: useRuntimeConfig().public.marketplaceAddress,
    AVAX: useRuntimeConfig().public.avaxMarketplaceAddress,
  }

  const isHalfProgressBg = {
    false: 'bg-warning',
    true: 'bg-success',
  }

  const twfChangesInfo = {
    false: {
      symbol: '-',
      color: 'text-error',
    },
    true: {
      symbol: '+',
      color: 'text-success',
    },
  }

  async function getActivePlans() {
    try {
      const res = await api.apiCall('GET', '/user/vesting-plan', {
        is_fully_claimed: 'false',
        take: 50,
      })

      activePlans.value = res.data.message.data
    }
    catch (error) {
      console.error('active plans error', error)
    }
  }

  async function getTokenPlans(network = currentNetwork.value) {
    try {
      // Check authentication before making API call
      if (!isLoggedIn.value) {
        console.warn('User not logged in, skipping token plans fetch')
        tokenPlans.value = []
        return
      }

      const res = await api.apiCall('GET', '/user/token-plan', {
        take: 50,
        network: network.toUpperCase(),
      })

      console.log('token plans', res.data.message.data)
      tokenPlans.value = res.data.message.data
    }
    catch (error) {
      console.error('token plans error', error)
    }
  }

  async function getClaimedPlans() {
    try {
      const res = await api.apiCall('GET', '/user/vesting-plan', {
        is_fully_claimed: 'true',
      })

      claimedPlans.value = res.data.message.data
    }
    catch (error) {
      console.error('active plans error', error)
    }
  }

  async function getUserListedLots() {
    try {
      // Check authentication before making API call
      if (!isLoggedIn.value) {
        console.warn('User not logged in, skipping listed lots fetch')
        userListedLots.value = []
        userListedLotsMeta.value = {}
        return
      }

      const res = await api.apiCall('GET', '/user/listing', {
        status: 0,
        take: 10,
        sort_by: 'LISTED_TIME',
      })

      userListedLots.value = res.data.message.data
      userListedLotsMeta.value = res.data.message.meta

      console.log('user listed lots', userListedLots.value)
    }
    catch (error) {
      console.error('user listed lots error', error)
    }
  }

  async function getUserSummary() {
    try {
      // Check authentication before making API call
      if (!isLoggedIn.value) {
        console.warn('User not logged in, skipping user summary fetch')
        userSummary.value = {}
        return
      }

      const res = await api.apiCall('GET', '/user/user-summary')
      userSummary.value = res.data.message

      console.log('user summary', userSummary.value)
    }
    catch (error) {
      console.error('user summary error', error)
    }
  }

  function getCountDown(time) {
    const unlockStart = $dayjs(time * 1)
    const diffInMs = unlockStart.diff($dayjs())
    if (diffInMs <= 0) {
      return null
    }
    const durationObj = $dayjs.duration(diffInMs)

    const days = Math.floor(durationObj.asDays()) // Total days
    const hours = Math.floor(durationObj.days()) // Remaining hours after extracting days
    const minutes = durationObj.minutes().toString().padStart(2, '0')

    return `${days}d : ${hours}h : ${minutes}m`
  }

  function toPlanInfo(plan, index, type = 'active') {
    localStorage.setItem(
      'active-plan',
      JSON.stringify({
        planId: plan.lot_id,
        planIndex: index,
      }),
    )

    localStorage.setItem('plan-type', type)

    router.push(`/myassets/${plan.token_address}`)
  }

  function getVestingProgress(start, end) {
    // Define your start and end dates
    const startDate = $dayjs(start * 1) // Replace with your start date
    const endDate = $dayjs(end * 1) // Replace with your end date
    const currentDate = $dayjs()

    // Calculate total duration in days
    const totalDays = endDate.diff(startDate, timeUnit)

    console.log('totalDays', totalDays)

    // Calculate elapsed days from the start date to the current date
    const elapsedDays = currentDate.diff(startDate, timeUnit)

    // Calculate the progress percentage
    const progressPercentage
      = elapsedDays >= totalDays
        ? 100 // If the current date is past the end date, set progress to 100%
        : ((elapsedDays / totalDays) * 100).toFixed(2)

    console.log(`Current progress: ${progressPercentage}%`)

    return progressPercentage
  }

  function getSellable(
    totalAllocated,
    marketAmount,
    claimedAmount,
    sellLimit,
    listedAmount,
  ) {
    const claimable = totalAllocated * 1 + marketAmount * 1 - claimedAmount * 1

    console.log(
      'sellable amount',
      totalAllocated,
      marketAmount,
      claimedAmount,
      sellLimit,
      listedAmount,
      claimable,
    )

    console.log({
      'claimable amount': claimable,
      'sell limit': sellLimit,
      'listed amount': listedAmount,
      'result 1': sellLimit * 1 - listedAmount * 1,
      'result 2': claimable,
    })

    console.log(
      'final return',
      claimable > sellLimit * 1 ? sellLimit * 1 : claimable,
    )

    return claimable > sellLimit * 1 ? sellLimit * 1 : claimable
  }

  function showListModal(plan) {
    // listModal.showModal();
    selectedPlan.value = plan
    document.getElementById('newListModal').checked = true
    console.log('selected plan', selectedPlan.value)
  }

  function showSummaryModal(plan) {
    selectedPlan.value = plan
    document.getElementById('planDetailsModal').checked = true
    console.log('selected plan from showSummaryModal', selectedPlan)
  }

  async function getActiveClaimable(network = currentNetwork.value) {
    // alert('hihi working now')
    const LOG_PREFIX = '[getActiveClaimable]'
    try {
      console.log(`${LOG_PREFIX} Starting function for network:`, network)

      // Create maps for vesting_id, token decimal, and token price lookups
      const addressToId = {}
      const addressToDecimal = {}
      const addressToPrice = {}

      // Only process token plans for the current network
      tokenPlans.value.forEach((token) => {
        console.log(`${LOG_PREFIX} Processing token:`, token.token_name)
        token.plans.forEach((plan) => {
          // if (plan.network === network) {
          console.log(
            `${LOG_PREFIX} Processing plan - vesting_address:`,
            plan.vesting_address,
          )
          console.log(
            `${LOG_PREFIX} Processing plan - vesting_id:`,
            plan.vesting_id,
          )
          addressToId[plan.vesting_address] = plan.vesting_id
          addressToDecimal[plan.vesting_address] = token.token_decimal
          addressToPrice[plan.vesting_address] = token.token_price
          // }
        })
      })

      console.log(`${LOG_PREFIX} Address to ID mapping:`, addressToId)
      console.log(
        `${LOG_PREFIX} Address to Decimal mapping:`,
        addressToDecimal,
      )
      console.log(`${LOG_PREFIX} Address to Price mapping:`, addressToPrice)

      const vestingAddresses = tokenPlans.value.flatMap(token =>
        token.plans.map(plan => plan.vesting_address),
      )

      console.log(
        `${LOG_PREFIX} All vesting addresses for network ${network}:`,
        vestingAddresses,
      )

      const contractPromises = vestingAddresses.map((address) => {
        console.log(`${LOG_PREFIX} Creating promise for address:`, address)
        return web3Store.getClaimable(address, currentNetwork.value)
      })
      console.log(
        `${LOG_PREFIX} Total promises created:`,
        contractPromises.length,
      )

      const results = await Promise.allSettled(contractPromises)
      console.log(`${LOG_PREFIX} Promise results:`, results)

      const claimables = results.reduce((acc, result, index) => {
        const address = vestingAddresses[index]
        const vestingId = addressToId[address]
        const decimal = addressToDecimal[address]
        const tokenPrice = addressToPrice[address]

        const rawAmount
          = result.status === 'fulfilled' ? result.value[0].toString() : '0'
        const tokenAmount
          = rawAmount === '0'
            ? '0'
            : (Number(rawAmount) / 10 ** decimal).toString()
        // const usdtValue = rawAmount === '0' ? '0' : (
        //   (Number(rawAmount) / Math.pow(10, decimal)) * (Number(tokenPrice) / Math.pow(10, network == 'AVAX' ? useRuntimeConfig().public.avaxUsdtDecimal : useRuntimeConfig().public.usdtDecimal))
        // ).toString();

        const usdtValue
          = rawAmount === '0'
            ? '0'
            : (Number(rawAmount) / 10 ** decimal)
              * (Number(tokenPrice) / 10 ** USDT_DECIMALS)

        console.log(`${LOG_PREFIX} Processing result:`, {
          address,
          vestingId,
          decimal,
          tokenPrice,
          rawAmount,
          tokenAmount,
          usdtValue,
          status: result.status,
          network,
        })

        acc[vestingId] = {
          tokenAmount,
          usdtValue,
        }
        return acc
      }, {})

      console.log(
        `${LOG_PREFIX} Final claimables object for network ${network}:`,
        claimables,
      )

      claimableAmounts.value = claimables

      calculateClaimableSum(claimables)

      return claimables
    }
    catch (error) {
      console.error(
        `${LOG_PREFIX} Error getting claimables for network ${network}:`,
        error,
      )
      return {}
    }
  }

  function calculateClaimableSum(claimables) {
    const LOG_PREFIX = '[calculateClaimableSum]'
    console.log(
      `${LOG_PREFIX} Starting calculation with claimables:`,
      claimables,
    )

    const sum = Object.values(claimables).reduce((total, claim) => {
      const usdtValue = Number(claim.usdtValue) || 0
      console.log(`${LOG_PREFIX} Adding USDT value:`, usdtValue)
      return total + usdtValue
    }, 0)

    console.log(`${LOG_PREFIX} Total sum:`, sum)

    claimableAmountSum.value = sum

    return sum
  }

  async function claimToken(address) {
    console.log('claimToken', address, currentNetwork.value)

    await web3Store.switchNetwork(currentNetwork.value)

    useSweetAlertStore().showLoadingAlert(
      'Claiming Tokens',
      'Proceed  in your wallet',
    )

    try {
      const result = await web3Kit.value?.claimToken(address)

      if (result) {
        console.log('claimToken result', result)
        await getTokenPlans()
        getActiveClaimable()
        // await getActivePlans();
        // await getClaimedPlans();
        await getUserSummary()
      }

      useSweetAlertStore().showAlert('Success', 'Claim Success', 'success')

      // changePlanType('claimed');
    }
    catch (error) {
      useSweetAlertStore().showAlert('error', 'Transaction Declined', 'error')
    }
  }

  web3Store.$onAction(async (action) => {
    console.log('action', action)
    if (action.name == 'disconnect') {
      navigateTo('/')
    }

    if (action.name == 'getAccountInfo') {
      setTimeout(async () => {
        try {
          getActiveClaimable()
          // web3Store.getClaimableTest();
          // await getMinListingDuration();
        }
        catch (err) {
          console.error('error loading myassets ($onAction)', err)
        }
        finally {
          isLoading.value = false
        }
      }, 1100)
    }
  })

  // delist function
  const minListDuration = ref(0)

  async function delist(lot) {
    console.log('delist lot', lot)

    try {
      useSweetAlertStore().showLoadingAlert('Processing', 'Delisting...')

      await web3Store.delistLot(
        lot.plan_contract_address,
        lot.list_id,
        lot.network_symbol,
      )

      await initMyAssets()

      useSweetAlertStore().showAlert('Success', 'Delist Success', 'success')
    }
    catch (error) {
      console.error(error)
      useSweetAlertStore().showAlert(
        'error',
        'Transaction Declined',
        'error',
      )
    }
  }

  async function delistWithPenalty(lot) {
    console.log('delist penalty lot', lot)

    const usdtBalance = await web3Store.getTokenBalance(
      multiUsdtAddress[currentNetwork.value],
      currentNetwork.value,
    )
    const penaltyFee = divideNumberUsingDecimals(
      lot.penalty_fee,
      USDT_DECIMALS,
    ).toString()

    if (penaltyFee * 1 > usdtBalance * 1) {
      useSweetAlertStore().showAlert('error', 'Insufficient USDT', 'error')
      return
    }

    const isApproveAllowance = await web3Store.checkAllowance(
      multiUsdtAddress[currentNetwork.value],
      multiMarketplaceAddress[currentNetwork.value],
    )

    if (!isApproveAllowance) {
      useSweetAlertStore().showLoadingAlert(
        'Processing',
        'Approving Allowance',
      )
      try {
        await web3Store.approveAllowance(
          multiUsdtAddress[currentNetwork.value],
          multiMarketplaceAddress[currentNetwork.value],
        )

        useSweetAlertStore().showConfirmAlert(
          `Allowance has been approved. Please click the confirm button to continue the action`,
          lot,
          delist,
        )
      }
      catch (error) {
        useSweetAlertStore().showAlert(
          'error',
          'Transaction Declined',
          'error',
        )
      }

      return
    }

    delist(lot)

    console.log('isApproveAllowance', isApproveAllowance)
  }

  async function delistConfirm(lot) {
    await web3Store.switchNetwork(lot.network_symbol)

    await getMinListingDuration(lot)

    console.log('delist lot', lot)
    const penaltyEnd = $dayjs(lot.listed_time * 1)
      .add(minListDuration.value, 'second')
      .format('x')

    console.log(
      'penalty end',
      $dayjs(penaltyEnd * 1).format('YYYY-MM-DD HH:mm:ss'),
    )

    // alert(penaltyEnd * 1);

    const now = $dayjs().format('x')

    console.log(
      'penalty time',
      lot.listed_time,
      penaltyEnd * 1,
      now * 1,
      penaltyEnd * 1 > now * 1,
      minListDuration.value,
    )

    if (penaltyEnd * 1 > now * 1) {
      const penaltyFee = lot.penalty_fee
      useSweetAlertStore().showConfirmAlert(
        `You have not yet reached the minimum listing duration. If you proceed with this action, a penalty fee (${
          penaltyFee * 1
        } USDT) will be applied.`,
        lot,
        delistWithPenalty,
        'Delist Confirmation',
        true,
      )
    }
    else {
      useSweetAlertStore().showConfirmAlert(
        `Please confirm if you would like to proceed with delisting ${common.formatLotId(
          lot.token_ticker,
          lot.display_id,
          lot.list_id,
        )}`,
        lot,
        delist,
      )
    }
  }

  async function getMinListingDuration(lot) {
    minListDuration.value = await web3Store.getMinListingDuration(
      lot.network_symbol,
    )
    // alert(minListDuration.value);
  }

  async function initMyAssets() {
    try {
      isLoading.value = true

      // Check if user is authenticated before making API calls
      if (!isLoggedIn.value) {
        console.warn(
          'User not authenticated, skipping My Assets initialization',
        )
        tokenPlans.value = []
        userListedLots.value = []
        userSummary.value = {}
        return
      }

      console.log('Initializing My Assets for authenticated user')

      // Add a small delay to ensure any recent transactions are processed
      await new Promise(resolve => setTimeout(resolve, 1000))

      // await getActivePlans();
      // await getClaimedPlans();
      await getTokenPlans()
      await getUserListedLots()
      await getUserSummary()
      getActiveClaimable()
      // if (web3Store.isLogin) {
      // await getMinListingDuration();
      //   getActiveClaimable();
      // }

      console.log('My Assets initialization completed successfully')
    }
    catch (error) {
      console.error('error loading my assets (initMyAssets)', error)
    }
    finally {
      isLoading.value = false
    }
  }

  // Watch for authentication state changes
  watch(isLoggedIn, async (newValue) => {
    if (newValue) {
      console.log('User authenticated, initializing My Assets data...')
      await initMyAssets()
    }
    else {
      console.log('User logged out, clearing My Assets data...')
      tokenPlans.value = []
      userListedLots.value = []
      userSummary.value = {}
      claimableAmounts.value = []
      claimableAmountSum.value = 0
    }
  })

  onMounted(async () => {
    currentNetwork.value
      = networksByChainId[networkData.value.chainId]?.nativeCurrency.symbol
        || currentNetwork.value

    try {
      useRouteStore().$patch({
        previousUrl: null,
        name: 'Listed Lots',
      })

      // Wait a bit for authentication state to be properly initialized
      await new Promise(resolve => setTimeout(resolve, 100))

      // Check authentication before making any API calls
      if (!isLoggedIn.value) {
        console.warn(
          'User not authenticated in composable onMounted, skipping API calls',
        )
        isLoading.value = false
        return
      }

      console.log('User authenticated, initializing My Assets data...')

      await getTokenPlans()
      // await getActivePlans();
      // await getClaimedPlans();
      await getUserListedLots()
      await getUserSummary()

      if (isLoggedIn.value) {
        getActiveClaimable()
        // await getMinListingDuration();
        // getActiveClaimable();

        // web3Store.getClaimableTest();
      }
    }
    catch (err) {
      console.error('error loading my assets (onMounted)', err)
    }
    finally {
      isLoading.value = false
    }
  })

  // Function to change network and load token plans only
  async function changeNetwork(networkId) {
    console.log('change network', networkId)
    if (networkId === currentNetwork.value)
      return

    isLoading.value = true

    try {
      // Update current network UI
      currentNetwork.value = networkId

      // Fetch token plans for the new network
      await getTokenPlans(networkId)

      // Get claimable amounts for the new network
      if (web3Store.isInit && tokenPlans.value.length > 0) {
        await getActiveClaimable(networkId)
      }
    }
    catch (error) {
      console.error('Error changing network:', error)
    }
    finally {
      isLoading.value = false
    }
  }

  return {
    getActivePlans,
    getUserListedLots,
    getUserSummary,
    getCountDown,
    delistConfirm,
    claimToken,
    initMyAssets,
    claimableAmounts,
    claimableAmountSum,
    showListModal,
    showSummaryModal,
    selectedPlan,
    getSellable,
    getVestingProgress,
    isHalfProgressBg,
    toPlanInfo,
    tokenPlans,
    twfChangesInfo,
    activePlans,
    claimedPlans,
    userListedLots,
    userListedLotsMeta,
    userSummary,
    isLoading,
    web3Store,
    currentNetwork,
    changeNetwork,
  }
}

import mocaBanner from "~/assets/images_new/marketplace/banners/moca_full_banner_1_5x.webp";
import memeBanner from "~/assets/images_new/marketplace/banners/meme_full_banner_1_5x.webp";
import scrollBanner from "~/assets/images_new/marketplace/banners/scroll_full_banner_1_5x.webp";
import mobileMocaBanner from "~/assets/images_new/marketplace/banners/moca_mobile_banner.webp";
import mobileMemeBanner from "~/assets/images_new/marketplace/banners/meme_mobile_banner.webp";
import mobileScrollBanner from "~/assets/images_new/marketplace/banners/scroll_mobile_banner.webp";

import solanaBanner from "~/assets/images_new/marketplace/banners/solana_3x.webp";
import avalancheBanner from "~/assets/images_new/marketplace/banners/avalanche_3x.webp";

// bid to win web banners
import bidToWinWebBanner from "~/assets/images_new/marketplace/banners/bid_to_win_web.png";
// bid to win mobile banners
import bidToWinMobileBanner from "~/assets/images_new/marketplace/banners/bid_to_win_mobile.png";

// prelaunch web banners
import solanaWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/solana_3x.webp";
import avalancheWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/avalanche_3x.webp";
import zksyncWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/zksync_3x.webp";
import ucoinWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/ucoin.png";
import sandboxWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/sandbox.png";
import midnightWebPrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch/midnight_claim.png";

// prelaunch mobile banners
import solanaMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/solana_mobile_3x.webp";
import avalancheMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/avalanche_mobile_3x.webp";
import zksyncMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/zksync_mobile_3x.webp";
import ucoinMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/ucoin_mobile.png";
import sandboxMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/sandbox_mobile.png";
import midnightMobilePrelaunchBanner from "~/assets/images_new/marketplace/banners/prelaunch_mobile/midnight_claim_mobile.png";

import { TIME_UNIT } from "~/utils/const";

// Global cache to prevent duplicate API calls (client-side only)
let apiCache = null;
let pendingRequests = null;

// Initialize cache only on client side
function initializeCache() {
  if (process.client && !apiCache) {
    apiCache = new Map();
    pendingRequests = new Map();
  }
}

export const useMarketplace = () => {
  const { $dayjs } = useNuxtApp();
  const timeUnit = TIME_UNIT;
  const route = useRoute();
  const web3Store = useWeb3Store();

  // Ensure we're on the client side for reactive data
  const isClient = process.client;

  // const mobileBanners = ref([
  //   {
  //     image: mobileMocaBanner
  //   },
  //   {
  //     image: mobileMemeBanner
  //   },
  //   {
  //     image: mobileScrollBanner
  //   }
  // ]);

  // const webBanners = ref([
  //   {
  //     image: solanaBanner
  //   },
  //   {
  //     image: avalancheBanner
  //   },
  //   {
  //     image: mocaBanner
  //   },
  //   {
  //     image: memeBanner
  //   },
  //   {
  //     image: scrollBanner
  //   }
  // ]);

  const webBanners = ref([
    {
      image: bidToWinWebBanner,
      url: "https://medium.com/secondswap-io/set-your-price-and-get-discounted-locked-tokens-0204059ba1dc",
      url_target: "_blank",
    },
    {
      image: avalancheWebPrelaunchBanner,
      url: "/bid-only-token/AVAX/?network=AVAX",
    },
    {
      image: solanaWebPrelaunchBanner,
      url: "/bid-only-token/SOL/?network=SOL",
    },
    {
      image: zksyncWebPrelaunchBanner,
      url: "/bid-only-token/ZK/?network=ZK",
    },
    {
      image: sandboxWebPrelaunchBanner,
      url: "/bid-only-token/SAND/?network=ETH",
    },
    {
      image: midnightWebPrelaunchBanner,
      url: useRuntimeConfig().public.midnightClaimPortalUrl,
      url_target: "_blank",
    },
  ]);

  const mobileBanners = ref([
    {
      image: bidToWinMobileBanner,
      url: "https://medium.com/secondswap-io/set-your-price-and-get-discounted-locked-tokens-0204059ba1dc",
      url_target: "_blank",
    },
    {
      image: avalancheMobilePrelaunchBanner,
      url: "/bid-only-token/AVAX/?network=AVAX",
    },
    {
      image: solanaMobilePrelaunchBanner,
      url: "/bid-only-token/SOL/?network=SOL",
    },
    {
      image: zksyncMobilePrelaunchBanner,
      url: "/bid-only-token/ZK/?network=ZK",
    },
    {
      image: sandboxMobilePrelaunchBanner,
      url: "/bid-only-token/SAND/?network=ETH",
    },
    {
      image: midnightMobilePrelaunchBanner,
      url: useRuntimeConfig().public.midnightClaimPortalUrl,
      url_target: "_blank",
    },
  ]);

  // Initialize with consistent values to prevent hydration mismatches
  const isLoading = ref(true);
  const bestDeals = ref([]);
  const unlockSoon = ref([]);
  const topTokens = ref([]);
  const bidOnlyTokens = ref([]);
  const allLots = ref([]);
  const allLotsMeta = ref(null);

  const activeTab = ref("bestDeal");

  const twfChangesInfo = {
    false: {
      symbol: "-",
      color: "text-error",
    },
    true: {
      symbol: "+",
      color: "text-success",
    },
  };

  // Cached API call function to prevent duplicate requests
  async function cachedApiCall(cacheKey, apiCallFn, ttl = 30000) {
    initializeCache();

    if (import.meta.server) {
      return await apiCallFn();
    }

    if (!apiCache || !pendingRequests) {
      return await apiCallFn();
    }

    const cached = apiCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < ttl) {
      return cached.data;
    }

    if (pendingRequests.has(cacheKey)) {
      return await pendingRequests.get(cacheKey);
    }

    const requestPromise = apiCallFn()
      .then((result) => {
        apiCache.set(cacheKey, {
          data: result,
          timestamp: Date.now(),
        });
        pendingRequests.delete(cacheKey);
        return result;
      })
      .catch((error) => {
        pendingRequests.delete(cacheKey);
        throw error;
      });

    pendingRequests.set(cacheKey, requestPromise);

    return await requestPromise;
  }

  async function getBestDeals() {
    if (!isClient) {
      return [];
    }

    return await cachedApiCall("best-deals", async () => {
      try {
        const res = await api.apiCall("GET", "/marketplace/best-deal", {
          order: "DESC",
          take: 50,
          sortBy: "BEST_DEAL",
        });
        console.log("best deals res", res);

        const data = res.data.message.data;
        bestDeals.value = data;
        return data;
      } catch (error) {
        console.error("best deals error", error);
        throw error;
      }
    });
  }

  async function getUnlockSoon() {
    if (!isClient) {
      return [];
    }

    return await cachedApiCall("unlock-soon", async () => {
      try {
        const res = await api.apiCall("GET", "/marketplace/best-deal", {
          order: "DESC",
          take: 50,
          sortBy: "UNLOCKING_SOON",
        });
        console.log("unlock soon res", res);

        const data = res.data.message.data;
        unlockSoon.value = data;
        return data;
      } catch (error) {
        console.error("unlock soon error", error);
        throw error;
      }
    });
  }

  async function getTopTokens() {
    if (!isClient) {
      return [];
    }

    return await cachedApiCall("top-tokens", async () => {
      try {
        const res = await api.apiCall("GET", "/marketplace/token-summary", {
          order: "DESC",
          take: 50,
          sortBy: "POPULAR",
        });
        console.log("top tokens res", res.data);

        const data = res.data.message.data;
        topTokens.value = data;
        return data;
      } catch (error) {
        console.error("top tokens error", error);
        throw error;
      }
    });
  }

  async function getBidOnlyTokens() {
    if (!isClient) {
      return [];
    }

    return await cachedApiCall("bid-only-tokens", async () => {
      try {
        const res = await api.apiCall("GET", "/marketplace/token-summary", {
          bid_only: true,
          order: "DESC",
          take: 50,
          sortBy: "POPULAR",
        });
        console.log("bid only tokens res", res.data);

        const data = res.data.message.data;
        bidOnlyTokens.value = data;
        return data;
      } catch (error) {
        console.error("bid only tokens error", error);
        throw error;
      }
    });
  }

  async function getAllLots(sort_by, order, token_name, page_number, network) {
    // Skip API calls on server side
    if (!isClient) {
      return [];
    }

    const cacheKey = `all-lots-${sort_by || "default"}-${order || "default"}-${
      token_name || "default"
    }-${page_number || 1}-${network || "all"}`;

    return await cachedApiCall(cacheKey, async () => {
      try {
        const res = await api.apiCall("GET", "/marketplace/all-lot", {
          ...(network && { network: network }),
          page: page_number,
          take: 10,
          sort_by: sort_by,
          order: order,
          token_name: token_name,
        });
        console.log("all lots res", res.data);

        const lotsData = res.data.message.lots.data;
        const lotsMeta = res.data.message.lots.meta;

        allLots.value = lotsData;
        allLotsMeta.value = lotsMeta;

        return lotsData;
      } catch (error) {
        console.error("all lots error", error);
        throw error;
      }
    });
  }

  function getCountDown(time) {
    console.log(
      "count down time",
      $dayjs.utc(time).format("YYYY-MM-DD HH:mm:ss")
    );
    const { unlockStart, now } = isNaN(time)
      ? {
          unlockStart: $dayjs(time),
          now: $dayjs(),
        }
      : {
          unlockStart: $dayjs.utc(time * 1),
          now: $dayjs.utc(), // Use UTC time for current time
        };
    const diffInMs = unlockStart.diff(now);
    if (diffInMs <= 0) {
      return null;
    }
    const durationObj = $dayjs.duration(diffInMs);

    const days = Math.floor(durationObj.asDays()); // Total days
    const hours = Math.floor(durationObj.hours()); // Remaining hours after extracting days
    const minutes = durationObj.minutes().toString().padStart(2, "0");

    return `${days}d : ${hours}h : ${minutes}m`;
  }

  // Flag to prevent multiple simultaneous refetches
  let isRefetching = false;

  async function refetch() {
    // Only refetch on client side to prevent hydration issues
    if (!isClient) {
      console.log("Skipping refetch on server side");
      return;
    }

    if (isRefetching) {
      console.log("Refetch already in progress, skipping...");
      return;
    }

    isRefetching = true;
    try {
      console.log("Starting refetch...");
      const [
        bestDealsResult,
        unlockSoonResult,
        topTokensResult,
        bidOnlyTokensResult,
        allLotsResult,
      ] = await Promise.allSettled([
        getBestDeals(),
        getUnlockSoon(),
        getTopTokens(),
        getBidOnlyTokens(),
        getAllLots(),
      ]);

      // Handle any failed requests gracefully
      const results = {
        bestDeals:
          bestDealsResult.status === "fulfilled" ? bestDealsResult.value : [],
        unlockSoon:
          unlockSoonResult.status === "fulfilled" ? unlockSoonResult.value : [],
        topTokens:
          topTokensResult.status === "fulfilled" ? topTokensResult.value : [],
        bidOnlyTokens:
          bidOnlyTokensResult.status === "fulfilled"
            ? bidOnlyTokensResult.value
            : [],
        allLots:
          allLotsResult.status === "fulfilled" ? allLotsResult.value : [],
      };

      console.log("Refetch completed successfully");
      return results;
    } catch (error) {
      console.error("Error during refetch:", error);
      throw error;
    } finally {
      isRefetching = false;
    }
  }

  onMounted(async () => {
    console.log("useMarketplace mounted");
    // Only fetch data on client side to prevent hydration mismatches
    if (process.client) {
      try {
        await refetch();
      } catch (error) {
        console.error("Error during initial data fetch:", error);
      }
    }
    isLoading.value = false;
  });

  // Function to clear cache when needed
  function clearCache() {
    initializeCache();
    if (apiCache && pendingRequests) {
      apiCache.clear();
      pendingRequests.clear();
      console.log("API cache cleared");
    }
  }

  return {
    mobileBanners,
    webBanners,
    isLoading,
    bestDeals,
    unlockSoon,
    topTokens,
    bidOnlyTokens,
    allLots,
    allLotsMeta,
    twfChangesInfo,
    getCountDown,
    activeTab,
    getBestDeals,
    getAllLots,
    refetch,
    clearCache,
  };
};

[{"inputs": [{"internalType": "uint256", "name": "_max<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"internalType": "address", "name": "_lotOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "ChangeMax<PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "_balance<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "userAddress", "type": "address"}], "name": "Whitelisted<PERSON><PERSON><PERSON>", "type": "event"}, {"inputs": [], "name": "lot<PERSON><PERSON>er", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ma<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_max<PERSON><PERSON><PERSON><PERSON>", "type": "uint256"}], "name": "set<PERSON>ax<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "totalWhitelist", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userSettings", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_userAddress", "type": "address"}], "name": "validateAddress", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "whitelist<PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]
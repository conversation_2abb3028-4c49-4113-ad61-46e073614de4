[{"inputs": [{"internalType": "address", "name": "_tokenIssuer", "type": "address"}, {"internalType": "address", "name": "_manager", "type": "address"}, {"internalType": "contract IERC20", "name": "_token", "type": "address"}, {"internalType": "uint256", "name": "_startTime", "type": "uint256"}, {"internalType": "uint256", "name": "_endTime", "type": "uint256"}, {"internalType": "uint256", "name": "_numOfSteps", "type": "uint256"}, {"internalType": "address", "name": "_vestingDeployer", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "target", "type": "address"}], "name": "AddressEmptyCode", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "AddressInsufficientBalance", "type": "error"}, {"inputs": [], "name": "AmountZero", "type": "error"}, {"inputs": [], "name": "ArrayLengthMismatch", "type": "error"}, {"inputs": [], "name": "Beneficiary<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "error"}, {"inputs": [], "name": "FailedInnerCall", "type": "error"}, {"inputs": [], "name": "GrantorNoVesting", "type": "error"}, {"inputs": [], "name": "InsufficientBalance", "type": "error"}, {"inputs": [], "name": "NewGrantorHasVesting", "type": "error"}, {"inputs": [], "name": "NothingToClaim", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beneficiary", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Claimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "grantor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newGrantor", "type": "address"}], "name": "ReallocateVesting", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "beneficiary", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "totalAmount", "type": "uint256"}], "name": "VestingCreated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "grantor", "type": "address"}, {"indexed": true, "internalType": "address", "name": "beneficiary", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "VestingTransferred", "type": "event"}, {"inputs": [{"internalType": "address", "name": "_beneficiary", "type": "address"}], "name": "available", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claim", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_beneficiary", "type": "address"}], "name": "claimable", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_totalAmount", "type": "uint256"}], "name": "createVesting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "_beneficiaries", "type": "address[]"}, {"internalType": "uint256[]", "name": "_totalAmounts", "type": "uint256[]"}], "name": "createVestings", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "endTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_beneficiary", "type": "address"}], "name": "getVesting", "outputs": [{"components": [{"internalType": "uint256", "name": "stepsClaimed", "type": "uint256"}, {"internalType": "uint256", "name": "amountClaimed", "type": "uint256"}, {"internalType": "uint256", "name": "releaseRate", "type": "uint256"}, {"internalType": "uint256", "name": "totalAmount", "type": "uint256"}, {"internalType": "uint256", "name": "claimable<PERSON><PERSON>", "type": "uint256"}], "internalType": "struct SecondSwap_Vesting.Vesting", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "manager", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_grantor", "type": "address"}, {"internalType": "address", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}, {"internalType": "uint256", "name": "_stepClaimed", "type": "uint256"}], "name": "managerTransferVesting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "numOfSteps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_grantor", "type": "address"}, {"internalType": "address", "name": "_NewGrantor", "type": "address"}], "name": "reallocateVesting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "startTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stepDuration", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token<PERSON>ssuer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_beneficiary", "type": "address"}], "name": "total", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_grantor", "type": "address"}, {"internalType": "address", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "transferVesting", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_beneficiary", "type": "address"}, {"internalType": "uint256", "name": "_claimableAmount", "type": "uint256"}], "name": "updateClaimableAmount", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "vestingDeployer", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]
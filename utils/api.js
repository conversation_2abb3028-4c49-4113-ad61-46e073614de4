import axios from "axios";
import { useWeb3Store } from "../stores/web3";

export default {
  async apiCall(method, endpoint, params = {}, isFormData = false) {
    const apiUrl = useRuntimeConfig().public.apiUrl;

    // console.log(baseUrl)
    let config = {};
    let errorMsg = "";

    if (method == "GET") {
      config = {
        withCredentials: true,
        params: params,
        timeout: 10000, // 10 second timeout
        headers: {
          // 'TRON-PRO-API-KEY':import.meta.env.VITE_API_KEY
        },
      };
    } else {
      config = {
        withCredentials: true,
        timeout: 10000, // 10 second timeout
        headers: {
          "Content-type": isFormData
            ? "multipart/form-data"
            : "application/json",
        },
      };
    }

    console.log(config);
    try {
      let response;
      if (method == "GET") {
        const api = await axios.get(apiUrl + endpoint, config);
        response = api;
      } else {
        if (isFormData) {
          console.log("isFormData");
          const formData = new FormData();

          Object.entries(params).forEach(([key, value]) => {
            console.log(`${key}: ${value}`);

            formData.append(key, value == null ? "" : value);
          });

          params = formData;
        }

        let api;
        switch (method.toUpperCase()) {
          case "DELETE":
            api = await axios.delete(apiUrl + endpoint, config);
            break;
          default:
            api = await axios.post(apiUrl + endpoint, params, config);
        }

        response = api;
      }

      return response;
    } catch (error) {
      errorMsg = error.response;
      // Handle different types of errors
      if (error.code === "ECONNABORTED") {
        console.error("Request timeout:", error.message);
        throw new Error("Request timeout. Please try again.");
      }

      if (error.response) {
        // Server responded with error status
        console.log("API Error Response:", error.response);

        if (error.response.status === 401) {
          useWeb3Store().disconnect();
          useNewWeb3Store().disconnect();
          navigateTo("/");
        } else if (error.response.status === 429) {
          console.error(
            "Rate limit exceeded. Please wait before making more requests."
          );
          throw new Error(
            "Too many requests. Please wait a moment and try again."
          );
        }
      } else if (error.request) {
        // Request was made but no response received
        console.error("No response received:", error.request);
        throw new Error("Network error. Please check your connection.");
      } else {
        // Something else happened
        console.error("Request setup error:", error.message);
      }

      throw error;
    }
  },
};

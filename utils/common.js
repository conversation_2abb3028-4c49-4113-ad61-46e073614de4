import numeral from 'numeral'
import { TIME_UNIT, USDT_PRICE_FORMAT } from './const'

export default {
  getActionName(action) {
    const prefixActions = {
      USER_LISTING: 'list',
      USER_DELIST: 'delist',
      USER_PURCHASE: 'purchase',
      USER_SELL: 'sell',
      USER_CLAIM: 'claim',
    }

    return prefixActions[action]
  },

  formatLotId(ticker, planId, lotId) {
    return `${planId.replace(ticker, '').substring(0, 2)}${numeral(lotId).format('0')}`
  },

  formatAddress(address) {
    const result = `${address.substring(
      0,
      4,
    )}..${address.substring(address.length - 4)}`

    return result
  },

  formatTxid(txid) {
    const result = `${txid.substring(
      0,
      6,
    )}..${txid.substring(txid.length - 6)}`

    return result
  },

  formatDateTime(ms, addAmount = 0) {
    const { $dayjs } = useNuxtApp()
    const timeUnit = TIME_UNIT

    const msNumber = ms * 1

    return $dayjs(msNumber).add(addAmount, timeUnit).format('DD-MM-YY HH:mm')
  },

  formatDateTimeV2(ms, addAmount = 0) {
    const { $dayjs } = useNuxtApp()
    const timeUnit = TIME_UNIT

    const msNumber = ms * 1

    return $dayjs(msNumber).add(addAmount, timeUnit).format('DD-MMM-YYYY')
  },

  formatDateTimeV3Date(dateString) {
    const { $dayjs } = useNuxtApp()
    return $dayjs(dateString).format('DD/MM/YY')
  },

  formatDateTimeV3Time(dateString) {
    const { $dayjs } = useNuxtApp()
    return $dayjs(dateString).format('HH:mm')
  },

  formatDateTimeV4(ms, addAmount = 0) {
    const { $dayjs } = useNuxtApp()
    const timeUnit = TIME_UNIT

    const msNumber = ms * 1

    return $dayjs(msNumber).add(addAmount, timeUnit).format('DD MMM YYYY - HH:mm A')
  },

  formatNumber(numberFormated) {
    const number = numberFormated.toString().replace(',', '')
    if (number >= 1e12) { // Trillions and above
      return numeral(number).format('0,0a') // e.g., "1.23t" for trillions
    }
    else if (number >= 1e9) { // Billions
      return numeral(number).format('0.00a') // e.g., "1.23b" for billions
    }
    else if (number >= 1e6) { // Millions
      return numeral(number).format('0.00a') // e.g., "1.23m" for millions
    }
    else {
      return numeral(number).format('0,0.00') // e.g., "123,456" for smaller numbers
    }
  },

  zeroToNil(number, isUSD = false) {
    const isSymbol = {
      false: '',
      true: '$',
    }
    return !Number(number) || number == '0.00' ? Array.from({ length: 12 }).fill('-').join('') : `${isSymbol[isUSD]}${number}`
  },

  formatZeroValue({ value, prefix, suffix }) {
    const zeroValue = Array.from({ length: 12 }).fill('-').join('')
    try {
      return toBigNumber(value).isEqualTo(0) || value == '0.00' ? zeroValue : `${prefix || ''}${value}${suffix || ''}`
    }
    catch (error) {
      return zeroValue
    }
  },

  twfSymbol(number) {
    const symbol = {
      false: '',
      true: '+',
    }

    return symbol[number]
  },

  twfColor(number) {
    const color = {
      false: 'text-error',
      true: 'text-success',
    }

    return color[number]
  },

  trendingColor(number) {
    const color = {
      false: 'stroke-error',
      true: 'stroke-success scale-y-[-1]',
    }

    return color[number]
  },

  getDomain(url) {
    if (!url) {
      return ''
    }

    const match = url.match(/^https?:\/\/([^/]+)/)
    return match ? match[1] : null
  },

  listType: {
    0: 'Partial',
    1: 'Single',
  },

  discountType: {
    0: 'No Discount',
    1: 'Linear',
    2: 'Fix Rate',
  },

  socialUrlPrefix: {
    twitter: 'https://x.com/',
    telegram: 'https://t.me/',
    github: '',
    discord: '',
  },

  formatSmallNumber(number, maxDecimals = 4) {
    // Handle exponential notation and convert to string
    let cleanNumber = typeof number === 'string'
      ? number.replace(/,/g, '')
      : String(number)

    // Convert exponential notation to decimal
    if (cleanNumber.includes('e')) {
      cleanNumber = Number(cleanNumber).toFixed(20)
    }

    // If the original input had commas, preserve the format
    if (typeof number === 'string' && number.includes(',')) {
      return number
    }

    const parts = cleanNumber.split('.')
    const hasDecimal = parts.length > 1

    // If no decimal or within max decimal places, return original
    if (!hasDecimal || parts[1].length <= maxDecimals) {
      return cleanNumber
    }

    // Count leading zeros after decimal
    let leadingZeros = 0
    const decimals = parts[1]
    for (let i = 0; i < decimals.length; i++) {
      if (decimals[i] === '0') {
        leadingZeros++
      }
      else {
        break
      }
    }

    // Get the remaining significant digits and remove trailing zeros
    const remainingDigits = decimals.slice(leadingZeros).replace(/0+$/, '')

    // Convert to subscript without using external libraries
    const subscript = String(leadingZeros).split('').map((digit) => {
      const subscripts = ['₀', '₁', '₂', '₃', '₄', '₅', '₆', '₇', '₈', '₉']
      return subscripts[parseInt(digit)]
    }).join('')

    return `0.0${subscript}${remainingDigits}`
  },

  formatUSDT(number) {
    if (number * 1 <= 0.0001) {
      return this.formatSmallNumber(number)
    }
    else {
      return numeral(number).format(USDT_PRICE_FORMAT, Math.floor)
    }
  },

  handleNumberInput(event, decimalPlaces = 2, maxValue = null) {
    const input = event.target
    console.log('common handleNumberInput input', input)
    const value = input.value
    const newValue = event.type === 'keypress'
      ? value + String.fromCharCode(event.charCode)
      : value

    const charCode = event.charCode || event.keyCode

    // Allow numbers and decimal point
    const isNumber = charCode >= 48 && charCode <= 57
    const isDecimal = charCode === 46

    // Check if decimal point is allowed (only one decimal point)
    console.log('common handleNumberInput value', value)
    const hasDecimal = value.includes('.')

    // Prevent input if:
    // 1. It's not a number or decimal point, or
    // 2. It's a decimal point but we already have one, or
    // 3. We already have max decimal places
    if (
      (!isNumber && !isDecimal)
      || (isDecimal && hasDecimal)
      || (hasDecimal && value.split('.')[1]?.length >= decimalPlaces && isNumber)
    ) {
      event.preventDefault()
    }
  },
  async decodeContractError(error) {
    const errorDecoder = ErrorDecoder.create()
    const decodedError = await errorDecoder.decode(error)

    console.error(`Revert reason: ${decodedError.reason}`)

    return decodedError.reason
  },
}

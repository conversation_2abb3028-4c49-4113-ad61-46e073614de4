@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --wui-color-modal-bg-base: #181b2b !important;
  --wui-color-gray-glass-002: #262938 !important;
}

@font-face {
  font-family: "Satoshi Black";
  src: url("../assets/fonts/satoshi/Satoshi-Black.otf");
}

@font-face {
  font-family: "Satoshi Medium";
  src: url("../assets/fonts/satoshi/Satoshi-Medium.otf");
}

@font-face {
  font-family: "Satoshi Bold";
  src: url("../assets/fonts/satoshi/Satoshi-Bold.otf");
}

@font-face {
  font-family: "Satoshi Regular";
  src: url("../assets/fonts/satoshi/Satoshi-Regular.otf");
}

@font-face {
  font-family: "Satoshi Light";
  src: url("../assets/fonts/satoshi/Satoshi-Light.otf");
}

@font-face {
  font-family: "Satoshi";
  src: url("../assets/fonts/satoshi/Satoshi-Black.otf");
  font-weight: 900;
}

@font-face {
  font-family: "Satoshi";
  src: url("../assets/fonts/satoshi/Satoshi-Bold.otf");
  font-weight: 700;
}

@font-face {
  font-family: "Satoshi";
  src: url("../assets/fonts/satoshi/Satoshi-Medium.otf");
  font-weight: 500;
}

@font-face {
  font-family: "Satoshi";
  src: url("../assets/fonts/satoshi/Satoshi-Regular.otf");
  font-weight: 400;
}

@font-face {
  font-family: "Satoshi";
  src: url("../assets/fonts/satoshi/Satoshi-Light.otf");
  font-weight: 300;
}

@layer components {
  .coin-border {
    @apply border-[0.1px] border-white/50 rounded-full;
  }

  .textarea {
    @apply border-0;
  }

  .bg-gradient {
    background: linear-gradient(180deg, #bcebff 0%, #48c3f8 50%);
  }

  .bg-gradient-dark {
    background: linear-gradient(
      180deg,
      rgba(188, 235, 255, 0.5) 0%,
      rgba(72, 195, 248, 0.5) 50%
    );
  }

  .btn {
    @apply !rounded-lg font-medium;
    /* height: 2.3rem !important;
    min-height: 2rem !important; */
  }

  .btn-gradient {
    @apply btn rounded-lg !text-base !text-black font-satoshiBold border-0;
    background: linear-gradient(360deg, #48c3f8 0%, #bcebff 72.5%);
    height: 2.3rem !important;
    min-height: 2rem !important;
  }

  .btn-gradient-dark {
    @apply btn rounded-lg text-base !text-black font-satoshiBold border-0;
    background: linear-gradient(
      180deg,
      rgba(188, 235, 255, 0.5) 0%,
      rgba(72, 195, 248, 0.5) 50%
    );
    height: 2.3rem !important;
    min-height: 2rem !important;
  }

  .badge-gradient {
    @apply badge text-white;
    background: linear-gradient(180deg, #22b4ff 0%, #0848c6 100%);
  }

  .text-shadow {
    text-shadow: 0px 2px 10px #000000;
  }

  .btn-solid {
    @apply btn rounded-lg text-base text-primary border-none;
    background-color: #0b242f;
  }

  .text-gradient {
    background: linear-gradient(90deg, #48c3f8 0%, #c55aea 64%, #ea5a73 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }

  .banner-gradient {
    background: linear-gradient(90deg, #48c3f8 0%, #c55aea 64%, #ea5a73 100%);
  }

  .circle-gradient {
    background-color: #256cec;
    filter: blur(50px);
  }

  .bg-glass {
    background: rgba(69, 75, 88, 0.3);
    backdrop-filter: blur(7.5px);
  }

  .tab {
    @apply text-white/50 !h-auto;
    border-color: transparent !important;
    width: 150px;
  }

  .tab:is(.tab-active) {
    @apply !border-primary border-b-2 text-white;
  }

  .item-blue-glass {
    position: absolute;
    top: 0;
    left: 0;
    background-image: url("~/assets/images/my-assets/card_mask_1_5x.webp");
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
  }

  .bg-token-info {
    background: linear-gradient(180deg, #48c3f8 0%, #0c4cac 100%);
  }

  #profileModal .input {
    height: 44px !important;
    min-height: 44px !important;
    /* @apply !bg-[#383F43] !p-2 !h-10 !min-h-10 focus:!ring-0 focus:!shadow-none focus:!outline-none !rounded-md !border-0; */
  }

  select:focus,
  .input-error:focus,
  .input-error:focus-within,
  .input:focus,
  .input:focus-within {
    @apply outline-none ring-0 !important;
  }

  .select {
    /* @apply !px-2 !h-10 !min-h-10 focus:ring-0 focus:shadow-none focus:outline-none rounded-md; */
  }

  .mt-fixed {
    @apply mt-[130px];
  }
  /* 
  .dropdown-hover:hover .dropdown-content {
    visibility: visible !important;
    opacity: 1 !important;
  }

  .dropdown-hover .dropdown-content {
    visibility: hidden !important;
    opacity: 0 !important;
  } */

  /* .dropdown-hover:focus-within .dropdown-content {
    visibility: hidden !important;
    opacity: 0 !important;
  } */
}

@layer utilities {
  .animate-pulse {
    animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  
  @keyframes skeleton-pulse {
    50% {
      opacity: 0.5;
    }
  }
}

/* sweet alert styles */
div:where(.swal2-container) div:where(.swal2-popup) {
  border: 1px solid #343849 !important;
  box-shadow: 0px 0px 14px 0px #ffffff14;
  @apply font-satoshiRegular min-w-[300px] max-w-[300px] md:min-w-[408px] md:max-w-[408px] p-5 !important;
}

.swal2-modal .swal2-html-container {
  @apply font-satoshiLight text-2sm text-white pt-3 !important;
}
.swal2-modal .swal2-title {
  @apply text-xl pt-6 font-satoshiLight;
}

.swal2-modal .swal2-close {
  @apply flex justify-end;
}
.swal2-modal .swal2-confirm {
  border: 1px solid #7cd3f8;
  @apply h-10 rounded-[8px] font-satoshiMedium !important;
}

.icon-height {
  @apply min-w-[125px] max-w-[125px] !important;
}
.buy-success-btn {
  @apply bg-primary text-base-100 !important;
}
.back-to-market-btn {
  @apply bg-success !important;
}
.swal2-modal .swal2-cancel {
  @apply bg-transparent text-primary py-0 mt-3 font-satoshiMedium !important;
}

.swal2-modal .swal2-deny:hover {
  background-color: transparent !important;
  background-image: unset !important;
}
.swal2-modal .swal2-cancel:hover {
  @apply bg-modal text-primary shadow-none !important;
}

.swal2-modal .swal2-close:hover {
  @apply text-white opacity-80;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.page-enter-active,
.page-leave-active {
  transition: all 0.4s;
}
.page-enter-from,
.page-leave-to {
  opacity: 0;
  filter: blur(1rem);
}

.spinner {
  width: 86px;
  height: 86px;
  border-radius: 50%;
  padding: 1.1px;
  background: conic-gradient(#0000 10%, #22b4ff) content-box;
  -webkit-mask: repeating-conic-gradient(
      #0000 0deg,
      #000 1deg 20deg,
      #0000 21deg 36deg
    ),
    radial-gradient(
      farthest-side,
      #0000 calc(100% - 9px),
      #000 calc(100% - 9px)
    );
  -webkit-mask-composite: destination-in;
  mask-composite: intersect;
  animation: spinner-d55elj 1s infinite steps(10);
}

@keyframes spinner-d55elj {
  to {
    transform: rotate(1turn);
  }
}

.loading-animation {
  width: 50.4px;
  height: 67.2px;
  --c: linear-gradient(#48c3f8 0 0);
  background: var(--c) 0% 50%, var(--c) 50% 50%, var(--c) 100% 50%;
  background-size: 10.1px 50%;
  background-repeat: no-repeat;
  animation: bars-wk19tf 1.2s infinite linear;
}

@keyframes bars-wk19tf {
  20% {
    background-position: 0% 0%, 50% 50%, 100% 50%;
  }

  40% {
    background-position: 0% 100%, 50% 0%, 100% 50%;
  }

  60% {
    background-position: 0% 50%, 50% 100%, 100% 0%;
  }

  80% {
    background-position: 0% 50%, 50% 50%, 100% 100%;
  }
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

.tab-active::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translate(-50%, 0);
  width: 5px;
  height: 5px;
  background-color: white;
  border-radius: 100%;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-box {
  box-shadow: 0px 0px 14px rgba(255, 255, 255, 0.08);
  border: 1px solid #343849;
}

@media (max-width: 376px) {
  .modal-box {
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
  }
}

.swal2-loader {
  display : none !important;
}

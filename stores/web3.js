import { defineStore } from "pinia";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  JsonRpcProvider,
  Contract,
  verifyMessage,
  formatEther,
  formatUnits,
  parseEther,
  parseUnits,
} from "ethers";
import { ErrorDecoder } from "ethers-decode-error";

import dayjs from "dayjs";
import advancedFormat from "dayjs/plugin/advancedFormat";

import planAbi from "../abi/plan.json";
import tokenAbi from "../abi/token.json";
import marketplaceSettingAbi from "../abi/marketplaceSetting.json";
import marketplaceAbi from "../abi/marketplace.json";

import whitelistAbi from "../abi/whitelist.json";

import {
  createAppKit,
  useAppKitAccount,
  useAppKitNetwork,
  useAppKitProvider,
  useDisconnect,
} from "@reown/appkit/vue";
import {
  mainnet,
  avalanche,
  sepolia,
  solana,
  solanaTestnet,
  solanaDevnet,
} from "@reown/appkit/networks";
import { EthersAdapter } from "@reown/appkit-adapter-ethers";
import { SolanaAdapter } from "@reown/appkit-adapter-solana/vue";
import {
  PhantomWalletAdapter,
  SolflareWalletAdapter,
} from "@solana/wallet-adapter-wallets";

const ethersAdapter = new EthersAdapter();
const solanaWeb3JsAdapter = new SolanaAdapter({
  wallets: [new PhantomWalletAdapter(), new SolflareWalletAdapter()],
});

import ethNetworkIcon from "~/assets/images_new/networks/eth_4x.webp";
import avaxNetworkIcon from "~/assets/images_new/networks/avax_4x.png";
import solanaNetworkIcon from "~/assets/images_new/networks/solana_4x.png";
import { USDT_DECIMALS } from "~/utils/const";

function calculateNetworkFee(feeData, estimatedGas) {
  if (!feeData) return -1n;
  if (feeData.maxFeePerGas) {
    return BigInt(estimatedGas) * feeData.maxFeePerGas;
  } else {
    return (
      BigInt(estimatedGas) *
      (feeData.gasPrice + (feeData.maxPriorityFeePerGas || 0n))
    );
  }
}

export const networkInfoById = {
  [avalanche.id]: {
    icon: avaxNetworkIcon,
    network: avalanche,
  },
  [sepolia.id]: {
    icon: ethNetworkIcon,
    network: sepolia,
  },
  [mainnet.id]: {
    icon: ethNetworkIcon,
    network: mainnet,
  },
  [solana.id]: {
    icon: solanaNetworkIcon,
    network: solana,
  },
  [solanaTestnet.id]: {
    icon: solanaNetworkIcon,
    network: solanaTestnet,
  },
  [solanaDevnet.id]: {
    icon: solanaNetworkIcon,
    network: solanaDevnet,
  },
};

export const useWeb3Store = defineStore("web3", () => {
  dayjs.extend(advancedFormat);

  const account = useAppKitAccount();
  const isApprovedAllowance = reactive({});

  const networks =
    useRuntimeConfig().public.web3Mode == "main"
      ? [mainnet, avalanche, solana]
      : [sepolia, avalanche, solanaDevnet];

  // 1. Get projectId from https://cloud.reown.com
  const projectId = useRuntimeConfig().public.reownProjectId;
  const appUrl = useRuntimeConfig().public.baseUrl;

  const metadata = {
    name: "SecondSwap",
    description: "",
    url: appUrl, // url must match your domain & subdomain
    icons: ["https://files-secondswap.secondswap.io/image_2swap.png"],
  };

  const modal = createAppKit({
    themeMode: "dark",
    themeVariables: {
      "--w3m-accent": "#7CD3F8",
    },
    adapters: [ethersAdapter, solanaWeb3JsAdapter],
    networks: networks,
    metadata,
    projectId,
    features: {
      swaps: false,
      onramp: false,
      email: false,
      socials: false,
      analytics: false, // Optional - defaults to your Cloud configuration
    },
    includeWalletIds: [
      "c57ca95b47569778a828d19178114f4db188b89b763c899ba0be274e97267d96",
      "4622a2b2d6af1c9844944291e5e7351a6aa24cd7b23099efac1b2fd875da31a0",
      "20459438007b75f4f4acb98bf29aa3b800550309646d375da5fd4aac6c2a2c66",
      "fd20dc426fb37566d803205b19bbc1d4096b248ac04548e3cfb6b3a38bd033aa",
      "0b415a746fb9ee99cce155c2ceca0c6f6061b1dbca2d722b3ba16381d0562150",
      "f323633c1f67055a45aac84e321af6ffe46322da677ffdd32f9bc1e33bafe29c",
      "a797aa35c0fadbfc1a53e7f675162ed5226968b44a19ee3d24385c64d1d3c393",
      "1ca0bdd4747578705b1939af023d120677c64fe6ca76add81fda36e350605e79",
      "f896cbca30cd6dc414712d3d6fcc2f8f7d35d5bd30e3b1fc5d60cf6c8926f98f",
    ],
  });

  const userInfo = reactive({
    address: null,
    isConnected: false,
    nativeBalance: 0,
  });

  const userWalletProvider = ref(null);

  const isLogin = ref(false);
  const isInit = ref(false);

  async function disconnect() {
    isLogin.value = false;

    useDisconnect().disconnect();

    localStorage.clear();

    await apiLogout();

    userInfo.address = null;
    userInfo.isConnected = false;
    userInfo.nativeBalance = 0;

    const route = useRoute();

    console.log("before navigate route", route.name);

    // navigateTo('/')

    const authPages = ["myassets", "myassets-token", "lotinfo", "profile"];

    // alert(route.name);

    if (authPages.includes(route.name)) {
      navigateTo("/");
    } else {
      return;
    }
  }

  async function apiLogout() {
    try {
      const res = await api.apiCall("POST", "/user/logout");
      console.log("logout res", res);
    } catch (error) {
      console.error("logout api res", error);
    }
  }

  function connectWallet() {
    modal.open();
  }

  function refreshWalletProvider() {
    const { walletProvider } = useAppKitProvider("eip155");
    if (walletProvider && typeof walletProvider.request === "function") {
      userWalletProvider.value = walletProvider;
      console.log("Wallet provider refreshed successfully");
    } else if (typeof window !== "undefined" && window.ethereum) {
      userWalletProvider.value = window.ethereum;
      console.log("Using window.ethereum as fallback provider");
    } else {
      console.warn("No valid wallet provider found");
      userWalletProvider.value = null;
    }
  }

  function getConfigByCurrentNetwork() {
    const networkData = useAppKitNetwork();
    console.log("networkData", networkData.value);
    switch (networkData.value.chainId) {
      case sepolia.id:
        return {
          marketplaceContract: useRuntimeConfig().public.marketplaceAddress,
          marketplaceSettingContract:
            useRuntimeConfig().public.marketplaceSettingAddress,
          jsonRpcUrl: useRuntimeConfig().public.jsonRpcUrl,
          usdtToken: useRuntimeConfig().public.usdtAddress,
          explorerUrl: useRuntimeConfig().public.explorerUrl,
          network: sepolia,
        };
      case mainnet.id:
        return {
          marketplaceContract: useRuntimeConfig().public.marketplaceAddress,
          marketplaceSettingContract:
            useRuntimeConfig().public.marketplaceSettingAddress,
          jsonRpcUrl: useRuntimeConfig().public.jsonRpcUrl,
          usdtToken: useRuntimeConfig().public.usdtAddress,
          explorerUrl: useRuntimeConfig().public.explorerUrl,
          network: mainnet,
        };
      case avalanche.id:
        return {
          marketplaceContract: useRuntimeConfig().public.avaxMarketplaceAddress,
          marketplaceSettingContract:
            useRuntimeConfig().public.avaxMarketplaceSettingAddress,
          jsonRpcUrl: useRuntimeConfig().public.avaxJsonRpcUrl,
          usdtToken: useRuntimeConfig().public.avaxUsdtAddress,
          explorerUrl: useRuntimeConfig().public.avaxExplorerUrl,
          network: avalanche,
        };
      case solana.id:
        return {
          network: solana,
        };
      case solanaDevnet.id:
        return {
          network: solanaDevnet,
        };
      case solanaTestnet.id:
        return {
          network: solanaTestnet,
        };
    }
    return {};
  }

  const networkData = useAppKitNetwork();
  async function getAccountInfo() {
    // since this is sdk for EVM, if we don't check, solana will throw error
    if (
      ![avalanche, sepolia, mainnet].find(
        (network) => network.id === networkData.value.chainId
      )
    ) {
      return;
    }

    if (process.client) {
      isLogin.value = localStorage.getItem("isLogin") == "true";
    }

    await new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 1000); // 1000ms = 1 second
    });

    const { address, isConnected } = account.value;
    const { walletProvider } = useAppKitProvider("eip155");

    console.log("watch account info", {
      isConnected: isConnected == true,
      walletProvider: walletProvider,
      providerType: typeof walletProvider,
    });

    userInfo.address = address;
    userInfo.isConnected = isConnected;

    // Only set the provider if it's valid
    if (walletProvider && typeof walletProvider.request === "function") {
      userWalletProvider.value = walletProvider;
    } else {
      console.warn("Invalid wallet provider received:", walletProvider);
      // Try to get provider from window.ethereum as fallback
      if (typeof window !== "undefined" && window.ethereum) {
        console.log("Using window.ethereum as fallback provider");
        userWalletProvider.value = window.ethereum;
      } else {
        userWalletProvider.value = null;
      }
    }

    isInit.value = true;

    if (isConnected) {
      console.log("get native balance", isConnected);
      getNativeBalance();
    } else {
      isLogin.value = false;
      localStorage.setItem("isLogin", false);
    }

    // if (address != undefined) {
    //     isLogin.value = true;
    // }
  }

  async function signMessage(content, timestamp = dayjs().format("x")) {
    console.log("wallet provider", userWalletProvider.value);

    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    console.log("ethers provider2", ethersProvider);

    const signer = await ethersProvider.getSigner();

    console.log("signer address", signer.address.toLowerCase());
    console.log("userInfo address", userInfo.address);

    if (signer.address.toLowerCase() != userInfo.address.toLowerCase()) {
      window.location.reload();
      return;
    }

    try {
      const message = content || `Welcome to SecondSwap ${timestamp}`;
      const signature = await signer.signMessage(message);
      // verifyMessage(message, signature);
      return {
        timestamp: timestamp,
        signature: signature,
        message: message,
      };
    } catch (error) {
      console.error(`error signing: ${error}`);
      throw error;
    }
  }

  function formatNumber(number, decimal = USDT_DECIMALS) {
    if (!Number.isNaN(Number(decimal))) decimal = decimal * 1;
    return formatUnits(number, decimal);
  }

  async function getClaimable(tokenAddress, networkId = "eth") {
    console.log("get claimable web3", tokenAddress, networkId);
    const readOnlyProvider = {
      ETH: new JsonRpcProvider(useRuntimeConfig().public.jsonRpcUrl),
      AVAX: new JsonRpcProvider(useRuntimeConfig().public.avaxJsonRpcUrl),
    };

    console.log("token address", tokenAddress, userWalletProvider.value);
    // const ethersProvider = new BrowserProvider(userWalletProvider.value);
    // const signer = await ethersProvider.getSigner();

    console.log(
      "readOnlyProvider",
      readOnlyProvider[networkId],
      planAbi,
      userInfo.address
    );

    const planContract = new Contract(
      tokenAddress,
      planAbi,
      readOnlyProvider[networkId]
    );
    const claimable = await planContract.claimable(userInfo.address);
    console.log("claimable from contract", claimable);
    return claimable;
  }

  async function getClaimableTest() {
    const ethersProvider = new JsonRpcProvider(
      useRuntimeConfig().public.avaxJsonRpcUrl
    );

    const planContract = new Contract(
      "******************************************",
      planAbi,
      ethersProvider
    );
    console.log("planContract", planContract, planAbi, userInfo.address);

    const claimable = await planContract.claimable(userInfo.address);
    console.log("testing claimable JS2001", claimable);
    return claimable;
  }

  async function getClaimableMulti(tokenAddressArr) {
    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const signer = await ethersProvider.getSigner();

    const callArr = [];

    tokenAddressArr.forEach((address) => {
      const planContract = new Contract(address, planAbi, signer);
      const claimable = planContract.claimable(userInfo.address);

      callArr.push(planContract.claimable(userInfo.address));
    });

    const results = await Promise.all(callArr);
    console.log("multicall results", results);

    return results;
  }

  async function claimToken(tokenAddress) {
    try {
      const ethersProvider = new BrowserProvider(userWalletProvider.value);
      const signer = await ethersProvider.getSigner();

      const planContract = new Contract(tokenAddress, planAbi, signer);
      const claimAction = await planContract.claim();
      await ethersProvider.waitForTransaction(claimAction.hash);

      return true;
    } catch (error) {
      let err = await decodeContractError(error);
      throw err;
    }
  }

  async function listLot(vestingAddress, listData, decimal, network = "ETH") {
    await switchNetwork(network);

    const config = getConfigByCurrentNetwork();

    const usdtAddress = config.usdtToken;
    let discountPct = {
      0: "0",
      1: (listData.linearDiscountPct * 100).toFixed(0).toString(),
      2: (listData.fixDiscountPct * 100).toFixed(0).toString(),
    };

    const data = [
      vestingAddress,
      parseUnits(listData.amount.toString(), decimal * 1),
      parseUnits(listData.pricePerToken.toString(), USDT_DECIMALS * 1),
      discountPct[listData.discountType],
      listData.fillType,
      listData.discountType,
      listData.maxWhitelist,
      usdtAddress,
      listData.listType,
    ];

    try {
      const ethersProvider = new BrowserProvider(userWalletProvider.value);
      const signer = await ethersProvider.getSigner();

      let minPurchaseAmount;
      if (listData.fillType == 0) {
        const rawValue = 1 / Number(listData.pricePerToken);
        const flooredValue = Math.floor(rawValue * 1000000) / 1000000;
        minPurchaseAmount = parseUnits(
          flooredValue.toFixed(6).toString(),
          decimal * 1
        );
      } else {
        minPurchaseAmount = parseUnits(listData.amount.toString(), decimal * 1);
      }

      const marketplaceContract = new Contract(
        config.marketplaceContract,
        marketplaceAbi,
        signer
      );
      const listAction = await marketplaceContract.listVesting(
        vestingAddress,
        parseUnits(listData.amount.toString(), decimal * 1),
        parseUnits(listData.pricePerToken.toString(), USDT_DECIMALS * 1),
        discountPct[listData.discountType],
        listData.fillType,
        listData.discountType,
        listData.maxWhitelist.toString(),
        config.usdtToken,
        minPurchaseAmount,
        listData.listType == 1
      );

      // Get transaction hash immediately
      const transactionHash = listAction.hash;

      // Wait for transaction and get receipt
      const receipt = await ethersProvider.waitForTransaction(listAction.hash);

      // Parse the Listed event from the transaction logs
      const listedEvent = receipt.logs.find((log) => {
        try {
          const parsedLog = marketplaceContract.interface.parseLog(log);
          return parsedLog.name === "Listed";
        } catch (e) {
          return false;
        }
      });

      let listingId = null;

      if (listedEvent) {
        const parsedEvent = marketplaceContract.interface.parseLog(listedEvent);
        listingId = parsedEvent.args.listingId.toString();
      } else {
        console.warn("Listed event not found in transaction logs");
      }

      return {
        transactionHash,
        listingId,
      };
    } catch (error) {
      console.error("Error in listLot:", error);

      // Provide more specific error messages
      if (error.message.includes("Wallet not connected")) {
        throw new Error("Please connect your wallet first");
      } else if (error.message.includes("Invalid wallet provider")) {
        throw new Error("Wallet connection lost. Please reconnect your wallet");
      } else if (error.message.includes("User rejected")) {
        throw new Error("Transaction was cancelled by user");
      } else if (error.message.includes("insufficient funds")) {
        throw new Error("Insufficient funds for transaction");
      } else {
        let err = await decodeContractError(error);
        throw err;
      }
    }
  }

  async function decodeContractError(error) {
    const errorDecoder = ErrorDecoder.create();
    const decodedError = await errorDecoder.decode(error);

    console.error(`Revert reason: ${decodedError.reason}`);

    return decodedError.reason;
  }

  async function getTokenBalance(tokenAddress, networkSymbol) {
    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const tokenContract = new Contract(tokenAddress, tokenAbi, ethersProvider);

    console.log(userInfo.address, tokenAddress);
    const balance = await tokenContract.balanceOf(userInfo.address);
    console.log(balance);

    // tokenBalance.value = formatEther(balance);

    // return formatEther(balance);

    return formatNumber(balance, USDT_DECIMALS);
  }

  async function delistLot(vestingPlan, listingId, network = "ETH") {
    await switchNetwork(network);

    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const signer = await ethersProvider.getSigner();
    const config = getConfigByCurrentNetwork();
    const marketplaceContract = new Contract(
      config.marketplaceContract,
      marketplaceAbi,
      signer
    );

    console.log("delist data", vestingPlan, listingId);
    try {
      const delistAction = await marketplaceContract.unlistVesting(
        vestingPlan,
        listingId
      );

      await ethersProvider.waitForTransaction(delistAction.hash);
    } catch (error) {
      console.error(error);
      let err = await decodeContractError(error);
      throw err;
    }
  }

  async function getMinListingDuration(network = "ETH") {
    await switchNetwork(network);

    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const config = getConfigByCurrentNetwork();
    const marketplaceSettingContract = new Contract(
      config.marketplaceSettingContract,
      marketplaceSettingAbi,
      ethersProvider
    );

    try {
      const minListingDuration =
        await marketplaceSettingContract.minListingDuration();

      return minListingDuration.toString() * 1;
    } catch (error) {
      console.error(error);
      let err = await decodeContractError(error);
      throw err;
    }
  }

  async function approveAllowance(tokenAddr, spender) {
    const isApprovedKey = `${tokenAddr}_${spender}`;
    const isApproved =
      isApprovedAllowance[isApprovedKey] ||
      (await checkAllowance(tokenAddr, spender));

    if (isApproved) {
      return true;
    }

    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const ethersSigner = await ethersProvider.getSigner();
    const tokenContract = new Contract(tokenAddr, tokenAbi, ethersSigner);

    const maxValue =
      "115792089237316195423570985008687907853269984665640564039457584007913129639935";
    // const approve = await tokenContract.approve(spender, maxValue);

    try {
      const approve = await tokenContract.approve(spender, maxValue);
      isApprovedAllowance[isApprovedKey] = true;
      const res = await ethersProvider.waitForTransaction(approve.hash);
      console.log("approve hash", res);

      return true;
      // isApproveUsdt.value = true;
    } catch (error) {
      let err = await decodeContractError(error);
      throw err;
    }
  }

  async function checkAllowance(tokenAddr, spender) {
    if (isApprovedAllowance[`${tokenAddr}_${spender}`]) {
      return true;
    }

    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const tokenContract = new Contract(tokenAddr, tokenAbi, ethersProvider);
    const allowance = await tokenContract.allowance(userInfo.address, spender);
    console.log("allowance", allowance);
    if (allowance > 0) {
      return true;
    }

    return false;
  }

  async function buyLot(buyParams) {
    console.log("buy params web3", buyParams);

    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const signer = await ethersProvider.getSigner();
    const config = getConfigByCurrentNetwork();
    const marketplaceContract = new Contract(
      config.marketplaceContract,
      marketplaceAbi,
      signer
    );

    console.log("buy params to contract", [
      buyParams.vestingPlan,
      buyParams.listingId,
      parseUnits(buyParams.amount.toString(), buyParams.tokenDecimal * 1),
      buyParams.referral ?? "******************************************",
    ]);

    try {
      const buyAction = await marketplaceContract.spotPurchase(
        buyParams.vestingPlan,
        buyParams.listingId,
        parseUnits(buyParams.amount.toString(), buyParams.tokenDecimal * 1),
        buyParams.referral ?? "******************************************"
      );

      await ethersProvider.waitForTransaction(buyAction.hash);
    } catch (error) {
      console.error("buy error", error);
      let err = await decodeContractError(error);
      throw err;
    }
  }

  async function checkIsWhitelist(whitelistAddress) {
    console.log("whitelist address", whitelistAddress);
    // const jsonRpcProvider = new JsonRpcProvider(jsonRpcUrl);
    const ethersProvider = new BrowserProvider(userWalletProvider.value);

    const whitelistContract = new Contract(
      whitelistAddress,
      whitelistAbi,
      ethersProvider
    );

    try {
      const isWhitelist = await whitelistContract.validateAddress(
        userInfo.address
      );
      console.log("isWhitelist", isWhitelist);
      return isWhitelist;
    } catch (error) {
      console.error(error);
    }
  }

  async function getWhitelistInfo(whitelistAddress) {
    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const whitelistContract = new Contract(
      whitelistAddress,
      whitelistAbi,
      ethersProvider
    );

    const available = await whitelistContract.totalWhitelist();
    const max = await whitelistContract.maxWhitelist();

    return {
      available: available.toString() * 1,
      max: max.toString() * 1,
    };
  }

  async function getNativeBalance() {
    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    console.log("ethers provider", ethersProvider);
    const balance = await ethersProvider.getBalance(userInfo.address);
    userInfo.nativeBalance = formatEther(balance);
  }

  async function switchNetwork(networkSymbol) {
    if (
      !userInfo.isConnected ||
      getConfigByCurrentNetwork().network.nativeCurrency.symbol ===
        networkSymbol
    ) {
      return;
    }
    const supportedNetworks = {
      // 1: 'mainnet',
      // 43114: 'avalanche',
      // 11155111: 'sepolia',
      // 43113: 'avalancheFuji'
      ETH: sepolia,
      AVAX: avalanche,
    };

    if (!supportedNetworks[networkSymbol]) {
      throw new Error(`Unsupported network ID: ${networkSymbol}`);
    }

    try {
      // await userWalletProvider.value.request({
      //     method: 'wallet_switchEthereumChain',
      //     params: [{ chainId: `0x${networkId.toString(16)}` }]
      // });

      useSweetAlertStore().showLoadingAlert("Processing", "Switching Network");
      await modal.switchNetwork(supportedNetworks[networkSymbol]);
      setTimeout(useSweetAlertStore().close, 1000);
      await new Promise((res) => setTimeout(res, 1010));

      await getAccountInfo();

      // Update provider after network switch
      const ethersProvider = new BrowserProvider(userWalletProvider.value);
      const network = await ethersProvider.getNetwork();
      console.log("Switched to network:", network.name);

      return network;
    } catch (error) {
      useSweetAlertStore().showAlert("error", "Switch network failed", "error");
      console.error("Network switch failed:", error);
      throw error;
    }
  }

  async function whitelistAddress(whitelistAddress) {
    const ethersProvider = new BrowserProvider(userWalletProvider.value);
    const signer = await ethersProvider.getSigner();
    const whitelistContract = new Contract(
      whitelistAddress,
      whitelistAbi,
      signer
    );

    try {
      const whitelistAction = await whitelistContract.whitelistAddress();

      await ethersProvider.waitForTransaction(whitelistAction.hash);
      return true;
    } catch (error) {
      let err = await decodeContractError(error);
      throw err;
    }
  }

  async function getMaxSupply(tokenAddress) {
    const config = getConfigByCurrentNetwork();
    console.log("max supply", tokenAddress, config.jsonRpcUrl);
    const jsonRpcProvider = new JsonRpcProvider(config.jsonRpcUrl);
    const tokenContract = new Contract(tokenAddress, tokenAbi, jsonRpcProvider);

    try {
      const maxSupply = await tokenContract.totalSupply();
      const decimals = await tokenContract.decimals();

      console.log(
        "max supply",
        formatNumber(maxSupply.toString(), decimals.toString())
      );
      return formatNumber(maxSupply.toString(), decimals.toString());
    } catch (error) {
      console.error("getMaxSupply contract error", error);
    }
  }

  async function getBuyLotNetworkFee(buyParams) {
    const config = getConfigByCurrentNetwork();
    const ethersProvider = new JsonRpcProvider(config.jsonRpcUrl);
    const marketplaceContract = new Contract(
      config.marketplaceContract,
      marketplaceAbi,
      ethersProvider
    );

    let estimatedGas = 0n;
    try {
      const isApproved = await checkAllowance(
        config.usdtToken,
        config.marketplaceContract
      );
      if (!isApproved) {
        await approveAllowance(config.usdtToken, config.marketplaceContract);
      }
      estimatedGas = await marketplaceContract.spotPurchase.estimateGas(
        buyParams.vestingPlan,
        buyParams.listingId,
        parseUnits(buyParams.amount.toString(), buyParams.tokenDecimal * 1),
        buyParams.referral ?? "******************************************",
        {
          from: userInfo.address,
        }
      );
    } catch (err) {
      console.error("failed to estimate gas", err);
      return -1n;
    }

    const feeData = await ethersProvider.getFeeData();
    return calculateNetworkFee(feeData, estimatedGas);
  }

  async function getListLotNetworkFee(vestingAddress, listData, decimal) {
    const config = getConfigByCurrentNetwork();

    let discountPct = {
      0: "0",
      1: (listData.linearDiscountPct * 100).toFixed(0).toString(),
      2: (listData.fixDiscountPct * 100).toFixed(0).toString(),
    };

    try {
      const ethersProvider = new JsonRpcProvider(config.jsonRpcUrl);
      console.log("jsonRpcUrl", config.jsonRpcUrl);
      const marketplaceContract = new Contract(
        config.marketplaceContract,
        marketplaceAbi,
        ethersProvider
      );

      let minPurchaseAmount;
      if (listData.fillType == 0) {
        const rawValue = 1 / Number(listData.pricePerToken);
        const flooredValue = Math.floor(rawValue * 1000000) / 1000000;
        minPurchaseAmount = parseUnits(
          flooredValue.toFixed(6).toString(),
          decimal * 1
        );
      } else {
        minPurchaseAmount = parseUnits(listData.amount.toString(), decimal * 1);
      }

      let estimatedGas = 0n;
      try {
        estimatedGas = await marketplaceContract.listVesting.estimateGas(
          vestingAddress,
          parseUnits(listData.amount.toString(), decimal * 1),
          parseUnits(listData.pricePerToken.toString(), USDT_DECIMALS * 1),
          discountPct[listData.discountType],
          listData.fillType,
          listData.discountType,
          listData.maxWhitelist.toString(),
          config.usdtToken,
          minPurchaseAmount,
          listData.listType == 1,
          {
            from: userInfo.address,
          }
        );
      } catch (err) {
        console.error("failed to estimate gas", err);
        return -1n;
      }

      const feeData = await ethersProvider.getFeeData();
      return calculateNetworkFee(feeData, estimatedGas);
    } catch (error) {
      console.error(error);
      let err = await decodeContractError(error);
      throw err;
    }
  }

  return {
    connectWallet,
    isInit,
    userInfo,
    isLogin,
    disconnect,
    getAccountInfo,
    signMessage,
    formatNumber,
    getClaimable,
    getClaimableMulti,
    claimToken,
    getTokenBalance,
    listLot,
    delistLot,
    getMinListingDuration,
    checkAllowance,
    approveAllowance,
    buyLot,
    checkIsWhitelist,
    getWhitelistInfo,
    whitelistAddress,
    getMaxSupply,
    getBuyLotNetworkFee,
    getListLotNetworkFee,
    getConfigByCurrentNetwork,
    networks,
    switchNetwork,
  };
});

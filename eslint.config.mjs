// eslint.config.mjs
import antfu from '@antfu/eslint-config'

export default antfu({
  rules: {
    // currently we don't have time to fix all eslint errors
    // so we disable all rules for now
    // should remove all rules in the future
    'unused-imports/no-unused-vars': 'off',
    'no-undef': 'off',
    'no-console': 'off',
    'no-unused-vars': 'off',
    'eqeqeq': 'off',
    'no-empty': 'off',
    'node/prefer-global/process': 'off',
    'no-unused-expressions': 'off',
    'node/prefer-global/buffer': 'off',
    'vue/require-v-for-key': 'off',
    'vue/eqeqeq': 'off',
    'vue/valid-v-for': 'off',
    'no-throw-literal': 'off',
    'no-alert': 'off',
    'vue/no-unused-vars': 'off',
    'vue/attribute-hyphenation': 'off',
    'ts/no-use-before-define': 'off',
    'unicorn/prefer-number-properties': 'off',
    'vue/no-unused-refs': 'off',
    'vue/require-explicit-emits': 'off',
    'style/no-tabs': 'off',
    'vue/prop-name-casing': 'off',
    'vue/require-toggle-inside-transition': 'off',
    'vue/no-irregular-whitespace': 'off',
    'no-irregular-whitespace': 'off',
    'vue/no-use-v-if-with-v-for': 'off',
    'regexp/no-unused-capturing-group': 'off',
    'vue/no-parsing-error': 'off',
  },
})
